{"version": "1.0", "name": "万师傅 MCP 服务配置", "description": "万师傅内部业务系统 MCP 能力服务，为通义 Lingma 提供业务工具支持", "servers": {"wanshifu-mcp": {"name": "万师傅业务服务", "displayName": "万师傅 MCP 服务", "endpoint": "https://your-mcp-server.com/mcp", "authentication": {"type": "api-key", "key": "lingma-api-key-2025", "header": "X-API-Key"}, "capabilities": {"tools": true, "resources": false, "prompts": false}, "settings": {"timeout": 30000, "retryCount": 3, "enableCache": true, "cacheTimeout": 300000, "logLevel": "info", "maxConcurrentRequests": 10}, "healthCheck": {"enabled": true, "interval": 60000, "endpoint": "/actuator/health"}}}, "defaultServer": "wanshifu-mcp", "globalSettings": {"enableAutoCompletion": true, "enableCodeSuggestion": true, "enableToolTips": true, "enableErrorReporting": true, "language": "zh-CN"}, "toolMappings": {"查询用户": {"mcpTool": "query_user", "description": "查询用户详细信息，包括基本信息、状态等", "category": "用户管理", "parameters": {"userId": {"type": "string", "description": "用户唯一标识符", "required": true, "example": "user001"}}, "examples": ["查询用户 user001 的信息", "获取用户详情", "用户信息查询", "查看用户状态"], "keywords": ["用户", "查询", "信息", "详情"]}, "创建用户": {"mcpTool": "create_user", "description": "创建新用户账户", "category": "用户管理", "parameters": {"username": {"type": "string", "description": "用户名", "required": true}, "email": {"type": "string", "description": "邮箱地址", "required": true}, "phone": {"type": "string", "description": "手机号码", "required": false}}, "examples": ["创建新用户", "注册用户账户", "添加用户"], "keywords": ["创建", "用户", "注册", "新建"]}, "查询订单": {"mcpTool": "query_order", "description": "查询订单信息，支持按状态筛选", "category": "订单管理", "parameters": {"userId": {"type": "string", "description": "用户ID", "required": true}, "status": {"type": "string", "description": "订单状态", "required": false, "enum": ["PENDING", "PAID", "SHIPPED", "DELIVERED", "CANCELLED"]}, "limit": {"type": "integer", "description": "返回数量限制", "required": false, "default": 10}}, "examples": ["查询用户 user001 的订单", "获取待处理订单", "查看订单状态", "订单列表查询"], "keywords": ["订单", "查询", "状态", "列表"]}, "创建订单": {"mcpTool": "create_order", "description": "创建新的服务订单", "category": "订单管理", "parameters": {"userId": {"type": "string", "description": "用户ID", "required": true}, "serviceType": {"type": "string", "description": "服务类型", "required": true, "enum": ["REPAIR", "INSTALL", "MAINTAIN", "CLEAN"]}, "description": {"type": "string", "description": "订单描述", "required": true}, "amount": {"type": "number", "description": "订单金额", "required": true}}, "examples": ["为用户创建维修订单", "新建服务订单", "创建安装订单"], "keywords": ["创建", "订单", "服务", "维修", "安装"]}, "更新订单状态": {"mcpTool": "update_order_status", "description": "更新订单状态", "category": "订单管理", "parameters": {"orderId": {"type": "string", "description": "订单ID", "required": true}, "status": {"type": "string", "description": "新状态", "required": true, "enum": ["PENDING", "PAID", "SHIPPED", "DELIVERED", "CANCELLED"]}, "reason": {"type": "string", "description": "状态变更原因", "required": false}}, "examples": ["更新订单状态为已支付", "订单状态变更", "修改订单状态"], "keywords": ["更新", "订单", "状态", "修改"]}, "发送通知": {"mcpTool": "send_notification", "description": "发送通知消息给用户", "category": "通知服务", "parameters": {"userId": {"type": "string", "description": "用户ID", "required": true}, "type": {"type": "string", "description": "通知类型", "required": true, "enum": ["SMS", "EMAIL", "PUSH"]}, "title": {"type": "string", "description": "通知标题", "required": true}, "content": {"type": "string", "description": "通知内容", "required": true}}, "examples": ["发送短信通知", "发送邮件提醒", "推送消息通知"], "keywords": ["发送", "通知", "短信", "邮件", "推送"]}}, "intelliSense": {"enableMcpTools": true, "showToolDescriptions": true, "enableParameterHints": true, "enableAutoImport": true, "customSnippets": [{"name": "万师傅用户查询", "prefix": "wsf-query-user", "body": ["// 查询万师傅用户信息", "const user = await mcpClient.callTool('query_user', {", "  userId: '${1:user001}'", "});", "console.log('用户信息:', user);"], "description": "快速生成用户查询代码"}, {"name": "万师傅订单创建", "prefix": "wsf-create-order", "body": ["// 创建万师傅订单", "const order = await mcpClient.callTool('create_order', {", "  userId: '${1:user001}',", "  serviceType: '${2:REPAIR}',", "  description: '${3:服务描述}',", "  amount: ${4:200.0}", "});", "console.log('订单创建结果:', order);"], "description": "快速生成订单创建代码"}]}, "errorHandling": {"enableRetry": true, "maxRetries": 3, "retryDelay": 1000, "enableFallback": true, "fallbackMessage": "万师傅 MCP 服务暂时不可用，请稍后重试"}, "logging": {"enabled": true, "level": "info", "includeRequestBody": false, "includeResponseBody": false, "maxLogSize": "10MB"}}
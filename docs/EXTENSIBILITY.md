# MCP 工具扩展性指南

本文档详细说明如何在万师傅MCP服务中添加新的工具，展示系统的高度扩展性。

## 🚀 核心特性

### 1. 注解驱动的工具注册
- 使用 `@McpTool` 注解标记工具方法
- 使用 `@McpToolParam` 注解定义参数
- 自动生成JSON Schema
- 零配置，即插即用

### 2. 自动发现机制
- 启动时自动扫描所有Spring Bean
- 发现并注册带有 `@McpTool` 注解的方法
- 支持热重载（开发环境）

### 3. 类型安全
- 基于Java反射的参数类型推断
- 自动类型转换
- 编译时检查

## 📝 添加新工具的步骤

### 步骤1: 创建工具类

```java
@Component
public class FileTools {
    
    @McpTool(
        name = "read_file",
        title = "读取文件",
        description = "读取指定路径的文件内容",
        group = "file",
        permissions = {"file:read"}
    )
    public Map<String, Object> readFile(
            @McpToolParam(
                name = "path",
                description = "文件路径",
                required = true,
                example = "/path/to/file.txt"
            ) String path,
            
            @McpToolParam(
                name = "encoding",
                description = "文件编码",
                required = false,
                defaultValue = "UTF-8",
                enumValues = {"UTF-8", "GBK", "ISO-8859-1"}
            ) String encoding
    ) {
        try {
            // 实现文件读取逻辑
            String content = Files.readString(Paths.get(path), 
                                            Charset.forName(encoding != null ? encoding : "UTF-8"));
            
            return Map.of(
                "success", true,
                "data", Map.of(
                    "path", path,
                    "content", content,
                    "size", content.length()
                ),
                "message", "文件读取成功"
            );
        } catch (Exception e) {
            return Map.of(
                "success", false,
                "message", "文件读取失败: " + e.getMessage()
            );
        }
    }
}
```

### 步骤2: 重启应用
工具会自动被发现和注册，无需额外配置！

### 步骤3: 验证工具
```bash
# 获取工具列表
curl -X POST http://localhost:8080/mcp/tools/list \
  -H "X-API-Key: demo-api-key-123456" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "params": {}, "id": "1"}'

# 调用新工具
curl -X POST http://localhost:8080/mcp/tools/call \
  -H "X-API-Key: demo-api-key-123456" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "read_file",
      "arguments": {
        "path": "/tmp/test.txt",
        "encoding": "UTF-8"
      }
    },
    "id": "2"
  }'
```

## 🎯 注解详解

### @McpTool 注解
```java
@McpTool(
    name = "tool_name",           // 工具唯一标识符（必需）
    title = "工具显示名称",        // 可选，用于UI显示
    description = "工具功能描述",   // 工具功能说明（必需）
    group = "tool_group",         // 工具分组，便于管理
    permissions = {"perm1", "perm2"}, // 所需权限
    requiresConfirmation = true   // 是否需要用户确认
)
```

### @McpToolParam 注解
```java
@McpToolParam(
    name = "param_name",          // 参数名称（必需）
    description = "参数说明",      // 参数描述（必需）
    required = true,              // 是否必需（默认true）
    type = "string",              // JSON Schema类型（可选，会自动推断）
    defaultValue = "default",     // 默认值
    enumValues = {"A", "B", "C"}, // 枚举值
    example = "示例值"            // 示例值
)
```

## 🔧 高级特性

### 1. 复杂返回类型
```java
@McpTool(name = "get_weather", description = "获取天气信息")
public ToolResult getWeather(@McpToolParam(name = "city") String city) {
    // 创建多种类型的内容
    List<Content> contents = Arrays.asList(
        new Content.TextContent("当前天气: 晴天"),
        new Content.ImageContent(base64Image, "image/png")
    );
    
    // 结构化数据
    Map<String, Object> structuredData = Map.of(
        "temperature", 25.5,
        "humidity", 60,
        "condition", "sunny"
    );
    
    return new ToolResult(contents, structuredData, false);
}
```

### 2. 异步工具
```java
@McpTool(name = "async_task", description = "异步任务")
public CompletableFuture<Map<String, Object>> asyncTask(
        @McpToolParam(name = "taskId") String taskId) {
    
    return CompletableFuture.supplyAsync(() -> {
        // 异步处理逻辑
        return Map.of("success", true, "result", "异步任务完成");
    });
}
```

### 3. 依赖注入
```java
@Component
public class DatabaseTools {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private OrderService orderService;
    
    @McpTool(name = "query_database", description = "数据库查询")
    public Map<String, Object> queryDatabase(
            @McpToolParam(name = "sql") String sql) {
        // 使用注入的服务
        return orderService.executeQuery(sql);
    }
}
```

## 🛡️ 安全考虑

### 1. 权限检查
```java
@McpTool(
    name = "delete_file",
    description = "删除文件",
    permissions = {"file:delete", "admin"},
    requiresConfirmation = true
)
public Map<String, Object> deleteFile(@McpToolParam(name = "path") String path) {
    // 权限会在调用前自动检查
    // requiresConfirmation = true 会要求用户确认
}
```

### 2. 输入验证
```java
@McpTool(name = "validate_input", description = "输入验证示例")
public Map<String, Object> validateInput(
        @McpToolParam(
            name = "email",
            description = "邮箱地址",
            type = "string"
        ) String email) {
    
    // 手动验证
    if (!email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
        return Map.of("success", false, "message", "邮箱格式不正确");
    }
    
    // 处理逻辑...
    return Map.of("success", true);
}
```

## 📊 工具分组管理

### 按功能分组
```java
// 用户相关工具
@Component
public class UserTools {
    @McpTool(name = "create_user", group = "user")
    public Map<String, Object> createUser(...) { }
    
    @McpTool(name = "update_user", group = "user")
    public Map<String, Object> updateUser(...) { }
}

// 文件相关工具
@Component  
public class FileTools {
    @McpTool(name = "read_file", group = "file")
    public Map<String, Object> readFile(...) { }
    
    @McpTool(name = "write_file", group = "file")
    public Map<String, Object> writeFile(...) { }
}
```

## 🔄 动态工具注册

### 运行时添加工具
```java
@Service
public class DynamicToolService {
    
    @Autowired
    private ToolRegistry toolRegistry;
    
    public void registerCustomTool(String name, String description, Method method, Object bean) {
        // 动态创建工具定义
        Tool tool = new Tool(name, description, generateSchema(method));
        ToolRegistry.ToolDefinition toolDef = new ToolRegistry.ToolDefinition(tool, bean, method);
        
        // 注册工具
        toolRegistry.registerTool(name, toolDef);
        
        // 通知客户端工具列表已更改
        toolRegistry.notifyToolsChanged();
    }
}
```

## 🧪 测试工具

### 单元测试
```java
@SpringBootTest
class CustomToolTest {
    
    @Autowired
    private ToolRegistry toolRegistry;
    
    @Autowired
    private ToolExecutor toolExecutor;
    
    @Test
    void testCustomTool() {
        // 获取工具定义
        ToolRegistry.ToolDefinition toolDef = toolRegistry.getToolDefinition("read_file");
        assertNotNull(toolDef);
        
        // 执行工具
        Map<String, Object> args = Map.of("path", "/tmp/test.txt");
        ToolResult result = toolExecutor.executeTool(toolDef, args);
        
        // 验证结果
        assertFalse(result.getIsError());
        assertNotNull(result.getContent());
    }
}
```

## 📈 性能优化

### 1. 缓存工具定义
```java
@Configuration
public class ToolCacheConfig {
    
    @Bean
    @ConditionalOnProperty(name = "mcp.tools.cache.enabled", havingValue = "true")
    public CacheManager toolCacheManager() {
        return new ConcurrentMapCacheManager("tools", "schemas");
    }
}
```

### 2. 异步执行
```java
@McpTool(name = "heavy_task", description = "重型任务")
@Async
public CompletableFuture<Map<String, Object>> heavyTask(
        @McpToolParam(name = "data") String data) {
    
    return CompletableFuture.supplyAsync(() -> {
        // 重型计算
        return processHeavyTask(data);
    });
}
```

通过这种设计，添加新的MCP工具变得极其简单，只需要：
1. 创建一个Spring组件
2. 添加 `@McpTool` 注解
3. 定义参数注解
4. 重启应用

系统会自动处理工具注册、Schema生成、类型转换等所有复杂工作！

# 部署指南

本文档提供万师傅 MCP 服务在不同环境下的部署最佳实践。

## 部署架构

### 推荐架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │      CDN        │
│    (Nginx)      │────│   (Kong/Zuul)   │────│   (CloudFlare)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Service Cluster                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   MCP Service   │   MCP Service   │      MCP Service            │
│   Instance 1    │   Instance 2    │      Instance N             │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Shared Services                              │
├─────────────────┬─────────────────┬─────────────────────────────┤
│     Apollo      │     Redis       │      Database               │
│  Config Center  │     Cache       │    (MySQL/PG)               │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 环境准备

### 系统要求

| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **CPU** | 2 核 | 4 核 | 支持 Java 21 |
| **内存** | 2GB | 4GB | JVM 堆内存 |
| **磁盘** | 20GB | 50GB | 日志和临时文件 |
| **网络** | 100Mbps | 1Gbps | API 响应性能 |

### 软件依赖

```bash
# Java 21 (必需)
java -version
# openjdk version "21.0.5" 2024-10-15

# Docker (推荐)
docker --version
# Docker version 24.0.0

# Docker Compose (推荐)
docker-compose --version
# docker-compose version 2.20.0
```

## 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 单机部署

```bash
# 1. 克隆项目
git clone https://github.com/wanshifu/mcp-service-demo.git
cd mcp-service-demo

# 2. 构建镜像
docker build -f docker/Dockerfile -t mcp-service:1.0.0 .

# 3. 启动服务
docker run -d \
  --name mcp-service \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e MCP_API_KEYS="prod-key-1,prod-key-2" \
  -v /opt/mcp/logs:/app/logs \
  --restart unless-stopped \
  mcp-service:1.0.0
```

#### 2. Docker Compose 部署

创建 `docker-compose.prod.yml`：

```yaml
version: '3.8'

services:
  mcp-service:
    image: mcp-service:1.0.0
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MCP_API_KEYS=${MCP_API_KEYS}
      - APOLLO_META_SERVER=${APOLLO_META_SERVER}
      - APOLLO_ENV=PROD
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - mcp-service
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  redis_data:
```

启动服务：

```bash
# 设置环境变量
export MCP_API_KEYS="prod-key-secure-123,backup-key-456"
export APOLLO_META_SERVER="http://apollo-server:8080"

# 启动服务栈
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f mcp-service
```

### 方式二：Kubernetes 部署

#### 1. 创建 Namespace

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: mcp-service
```

#### 2. 配置 ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: mcp-config
  namespace: mcp-service
data:
  application.yml: |
    server:
      port: 8080
    spring:
      profiles:
        active: prod
    mcp:
      protocol-version: "2025-06-18"
      tools:
        enabled: true
        rate-limit:
          requests-per-minute: 500
    logging:
      level:
        com.wanshifu.mcp: INFO
```

#### 3. 创建 Secret

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: mcp-secrets
  namespace: mcp-service
type: Opaque
data:
  api-keys: cHJvZC1rZXktc2VjdXJlLTEyMw==  # base64 encoded
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ==
```

#### 4. 部署应用

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-service
  namespace: mcp-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcp-service
  template:
    metadata:
      labels:
        app: mcp-service
    spec:
      containers:
      - name: mcp-service
        image: mcp-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: MCP_API_KEYS
          valueFrom:
            secretKeyRef:
              name: mcp-secrets
              key: api-keys
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: mcp-config
      - name: logs-volume
        emptyDir: {}
```

#### 5. 创建 Service

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mcp-service
  namespace: mcp-service
spec:
  selector:
    app: mcp-service
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
```

#### 6. 配置 Ingress

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mcp-ingress
  namespace: mcp-service
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - mcp-api.yourcompany.com
    secretName: mcp-tls
  rules:
  - host: mcp-api.yourcompany.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mcp-service
            port:
              number: 80
```

部署到 Kubernetes：

```bash
# 应用所有配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
kubectl apply -f ingress.yaml

# 检查部署状态
kubectl get pods -n mcp-service
kubectl get svc -n mcp-service
kubectl get ingress -n mcp-service

# 查看日志
kubectl logs -f deployment/mcp-service -n mcp-service
```

### 方式三：传统部署

#### 1. 系统准备

```bash
# 安装 Java 21
sudo apt update
sudo apt install openjdk-21-jdk

# 创建应用用户
sudo useradd -r -s /bin/false mcp-service
sudo mkdir -p /opt/mcp-service/{bin,config,logs}
sudo chown -R mcp-service:mcp-service /opt/mcp-service
```

#### 2. 应用部署

```bash
# 构建应用
mvn clean package -DskipTests

# 复制文件
sudo cp target/mcp-service-demo-1.0.0-SNAPSHOT.jar /opt/mcp-service/bin/
sudo cp src/main/resources/application-prod.yml /opt/mcp-service/config/

# 创建启动脚本
sudo tee /opt/mcp-service/bin/start.sh << 'EOF'
#!/bin/bash
cd /opt/mcp-service
exec java -Xms1g -Xmx2g \
  -Dspring.profiles.active=prod \
  -Dspring.config.location=classpath:/application.yml,/opt/mcp-service/config/application-prod.yml \
  -jar bin/mcp-service-demo-1.0.0-SNAPSHOT.jar
EOF

sudo chmod +x /opt/mcp-service/bin/start.sh
```

#### 3. 系统服务配置

```bash
# 创建 systemd 服务文件
sudo tee /etc/systemd/system/mcp-service.service << 'EOF'
[Unit]
Description=MCP Service
After=network.target

[Service]
Type=simple
User=mcp-service
Group=mcp-service
WorkingDirectory=/opt/mcp-service
ExecStart=/opt/mcp-service/bin/start.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

Environment=MCP_API_KEYS=prod-key-1,prod-key-2
Environment=APOLLO_META_SERVER=http://apollo-server:8080
Environment=APOLLO_ENV=PROD

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable mcp-service
sudo systemctl start mcp-service

# 检查服务状态
sudo systemctl status mcp-service
sudo journalctl -u mcp-service -f
```

## 负载均衡配置

### Nginx 配置

```nginx
# /etc/nginx/sites-available/mcp-service
upstream mcp_backend {
    least_conn;
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8081 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8082 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name mcp-api.yourcompany.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name mcp-api.yourcompany.com;

    ssl_certificate /etc/ssl/certs/mcp-api.crt;
    ssl_certificate_key /etc/ssl/private/mcp-api.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://mcp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    location /actuator/health {
        proxy_pass http://mcp_backend;
        access_log off;
    }
}
```

## 监控和日志

### 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'mcp-service'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
```

### 日志配置

```yaml
# logback-spring.xml
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/opt/mcp-service/logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>/opt/mcp-service/logs/application.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 安全加固

### 1. 网络安全

```bash
# 防火墙配置
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 8080/tcp   # 禁止直接访问应用端口
sudo ufw enable
```

### 2. 应用安全

```yaml
# application-prod.yml
server:
  # 隐藏服务器信息
  server-header: ""
  
  # SSL 配置
  ssl:
    enabled: true
    key-store: /opt/mcp-service/ssl/keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    
management:
  endpoints:
    web:
      exposure:
        # 只暴露必要的端点
        include: health,info,metrics
  endpoint:
    health:
      show-details: never
```

### 3. 系统加固

```bash
# 限制文件权限
sudo chmod 600 /opt/mcp-service/config/application-prod.yml
sudo chmod 700 /opt/mcp-service/bin/
sudo chmod 750 /opt/mcp-service/logs/

# 设置日志轮转
sudo tee /etc/logrotate.d/mcp-service << 'EOF'
/opt/mcp-service/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 mcp-service mcp-service
    postrotate
        systemctl reload mcp-service
    endscript
}
EOF
```

## 部署验证

### 健康检查

```bash
# 基础健康检查
curl -f https://mcp-api.yourcompany.com/actuator/health

# MCP 协议测试
curl -X POST https://mcp-api.yourcompany.com/mcp/initialize \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"jsonrpc":"2.0","method":"initialize","id":"1"}'
```

### 性能测试

```bash
# 使用 Apache Bench 进行压力测试
ab -n 1000 -c 10 -H "X-API-Key: your-api-key" \
   https://mcp-api.yourcompany.com/actuator/health

# 使用 wrk 进行性能测试
wrk -t12 -c400 -d30s -H "X-API-Key: your-api-key" \
    https://mcp-api.yourcompany.com/mcp/tools/list
```

## 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   sudo journalctl -u mcp-service -n 50
   
   # 检查端口占用
   sudo netstat -tlnp | grep 8080
   
   # 检查配置文件
   java -jar app.jar --spring.config.location=config/application-prod.yml --debug
   ```

2. **内存不足**
   ```bash
   # 调整 JVM 参数
   -Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
   ```

3. **连接超时**
   ```bash
   # 检查网络连接
   telnet mcp-api.yourcompany.com 443
   
   # 检查 DNS 解析
   nslookup mcp-api.yourcompany.com
   ```

### 监控告警

```yaml
# alertmanager 规则
groups:
- name: mcp-service
  rules:
  - alert: MCPServiceDown
    expr: up{job="mcp-service"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "MCP Service is down"
      
  - alert: MCPHighErrorRate
    expr: rate(http_server_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
```

# 架构设计文档

万师傅 MCP 服务采用现代化的分层架构设计，基于 Spring Boot 3.3.5 和 Java 21 构建，完全符合 MCP 2025-06-18 协议规范。

> 📝 **IDEA 用户注意**: 本文档提供了多种格式的架构图，包括 ASCII 文本图、简化 Mermaid 图和表格形式，确保在 IntelliJ IDEA 中能够完美预览。如果 Mermaid 图显示异常，请查看对应的 ASCII 版本。

## 系统架构概览

### 整体架构图

#### 方式一：简化架构图（IDEA 兼容）

```
┌─────────────────────────────────────────────────────────────────┐
│                    🤖 AI 客户端层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Claude Desktop │   Cursor IDE    │    通义 Lingma             │
│     桌面应用     │   代码编辑器     │    智能编程助手              │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🛡️ 网络安全层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   HTTPS/TLS     │    CDN/WAF      │    Rate Limiting            │
│    传输加密      │  内容分发/防火墙  │     请求限流                │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🚪 API 网关层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Load Balancer  │   API Gateway   │  Service Discovery          │
│    负载均衡      │    API网关      │     服务发现                │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🎯 MCP 服务层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Authentication │  Authorization  │   Tool Management           │
│    身份认证      │    权限控制      │     工具管理                │
├─────────────────┼─────────────────┼─────────────────────────────┤
│Request Validation│Response Format  │    Audit Log                │
│    请求验证      │   响应格式化     │    审计日志                 │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    💼 业务适配层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   User Service  │  Order Service  │ Notification Service        │
│    用户服务      │   订单服务       │     通知服务                │
├─────────────────┼─────────────────┼─────────────────────────────┤
│  Data Mapping   │ Error Handling  │ Transaction Management      │
│   数据映射       │   错误处理       │     事务管理                │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🏢 内部业务系统                               │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ User Management │  Order System   │   Payment System            │
│  用户管理系统    │  订单管理系统    │     支付系统                │
├─────────────────┼─────────────────┼─────────────────────────────┤
│   CRM System    │   ERP System    │   Data Warehouse            │
│ 客户关系管理     │ 企业资源规划     │     数据仓库                │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🏗️ 基础设施层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Apollo Config  │   Redis Cache   │   MySQL Database            │
│    配置中心      │    缓存服务      │    关系数据库               │
├─────────────────┼─────────────────┼─────────────────────────────┤
│   Monitoring    │     Logging     │      Security               │
│    监控系统      │    日志系统      │     安全组件                │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

#### 方式二：分层架构表格

| 层级 | 组件 | 功能描述 | 技术实现 |
|------|------|----------|----------|
| **🤖 AI 客户端层** | Claude Desktop | 原生 MCP 协议支持 | Anthropic 官方客户端 |
| | Cursor IDE | 智能代码补全 | VS Code 插件生态 |
| | 通义 Lingma | 中文智能交互 | 阿里云 AI 编程助手 |
| | 自定义应用 | 完全自定义集成 | HTTP API 调用 |
| **🛡️ 网络安全层** | HTTPS/TLS | 传输层加密 | SSL/TLS 1.3 |
| | CDN/WAF | 内容分发和防护 | CloudFlare/AWS |
| | Rate Limiting | 请求频率控制 | Redis + Lua 脚本 |
| | IP Whitelist | 访问控制 | Nginx/Spring Security |
| **🚪 API 网关层** | Load Balancer | 负载均衡 | Nginx/HAProxy |
| | API Gateway | 统一入口 | Kong/Zuul |
| | Service Discovery | 服务发现 | Consul/Eureka |
| **🎯 MCP 服务层** | Authentication | 身份认证 | API Key/JWT |
| | Authorization | 权限控制 | RBAC 模型 |
| | Tool Management | 工具管理 | 注解驱动注册 |
| | Request Validation | 请求验证 | JSON Schema |
| | Response Formatting | 响应格式化 | JSON-RPC 2.0 |
| | Audit Log | 审计日志 | 结构化日志 |
| **💼 业务适配层** | User Service | 用户服务 | Spring Boot |
| | Order Service | 订单服务 | 微服务架构 |
| | Notification Service | 通知服务 | 消息队列 |
| | Data Mapping | 数据映射 | MapStruct |
| | Error Handling | 错误处理 | 全局异常处理 |
| | Transaction Management | 事务管理 | Spring Transaction |
| **🏢 内部业务系统** | User Management | 用户管理系统 | 现有业务系统 |
| | Order System | 订单管理系统 | 现有业务系统 |
| | Payment System | 支付系统 | 第三方集成 |
| | CRM System | 客户关系管理 | 现有业务系统 |
| | ERP System | 企业资源规划 | 现有业务系统 |
| | Data Warehouse | 数据仓库 | 大数据平台 |
| **🏗️ 基础设施层** | Apollo Config | 配置中心 | 携程 Apollo |
| | Redis Cache | 缓存服务 | Redis Cluster |
| | MySQL Database | 关系数据库 | MySQL 8.0+ |
| | Monitoring | 监控系统 | Prometheus + Grafana |
| | Logging | 日志系统 | ELK Stack |

#### 方式三：简化 Mermaid 图（兼容性更好）

```mermaid
graph TD
    A[AI 客户端层] --> B[网络安全层]
    B --> C[API 网关层]
    C --> D[MCP 服务层]
    D --> E[业务适配层]
    E --> F[内部业务系统]

    G[基础设施层]
    D -.-> G
    E -.-> G

    subgraph "AI 客户端"
        A1[Claude Desktop]
        A2[Cursor IDE]
        A3[通义 Lingma]
    end

    subgraph "核心服务"
        D1[认证授权]
        D2[工具管理]
        D3[请求处理]
    end

    subgraph "业务服务"
        E1[用户服务]
        E2[订单服务]
        E3[通知服务]
    end

    A1 --> D1
    A2 --> D2
    A3 --> D3

    D1 --> E1
    D2 --> E2
    D3 --> E3
```

### 架构特点

| 特性 | 描述 | 技术实现 |
|------|------|----------|
| **分层解耦** | 清晰的分层架构，职责分离 | Spring Boot 分层架构 |
| **协议标准** | 完全符合 MCP 2025-06-18 规范 | JSON-RPC 2.0 实现 |
| **安全可靠** | 多层安全防护机制 | Spring Security + 自定义认证 |
| **高可扩展** | 注解驱动的工具注册机制 | 反射 + Spring AOP |
| **配置中心** | 动态配置管理 | Apollo 配置中心 |
| **监控完备** | 全链路监控和日志 | Spring Actuator + 自定义指标 |

## MCP 协议交互流程

### 协议握手和工具调用流程

#### 方式一：表格式流程图（最佳对齐）

| 步骤 | 🤖 AI客户端 | 🚪 API网关 | 🎯 MCP服务 | 💼 业务服务 | 🏢 内部系统 |
|------|-------------|------------|------------|------------|------------|
| **1. 协议初始化** | | | | | |
| 1.1 | `POST /mcp/initialize` → | | | | |
| 1.2 | | `验证API Key + 转发` → | | | |
| 1.3 | | | `协议版本协商` ↔ | | |
| 1.4 | | ← `返回服务能力信息` | | | |
| 1.5 | ← `协议确认响应` | | | | |
| **2. 工具发现** | | | | | |
| 2.1 | `POST /mcp/tools/list` → | | | | |
| 2.2 | | `转发请求` → | | | |
| 2.3 | | | `扫描注册的工具` ↔ | | |
| 2.4 | | ← `返回工具列表` | | | |
| 2.5 | ← `工具清单响应` | | | | |
| **3. 工具调用** | | | | | |
| 3.1 | `POST /mcp/tools/call` → | | | | |
| 3.2 | | `验证权限 + 转发` → | | | |
| 3.3 | | | `调用对应业务方法` → | | |
| 3.4 | | | | `执行业务逻辑` → | |
| 3.5 | | | | ← `返回业务结果` | |
| 3.6 | | | ← `格式化响应数据` | | |
| 3.7 | | | `记录审计日志` ↔ | | |
| 3.8 | | ← `返回执行结果` | | | |
| 3.9 | ← `最终响应` | | | | |

#### 方式二：文本版流程图（IDEA 完全兼容）

```
🤖 AI客户端        🚪 API网关        🎯 MCP服务        💼 业务服务        🏢 中台服务
      │                │                │                │                │
      │                │                │                │                │
      │ ═══════════════ 1. 协议初始化阶段 ══════════════════════════════════ │
      │                │                │                │                │
      ├───────────────→│                │                │                │
      │ POST /mcp/initialize            │                │                │
      │ {"method":"initialize"}         │                │                │
      │                │                │                │                │
      │                ├───────────────→│                │                │
      │                │ 验证API Key     │                │                │
      │                │ + 转发请求       │                │                │
      │                │                │                │                │
      │                │────────────────┤                │                │
      │                │协议版本协商      │                │                │
      │                │                │                │                │
      │                │←───────────────┤                │                │
      │                │ 返回服务能力信息  │                │                │
      │                │                │                │                │
      │←───────────────┤                │                │                │
      │ {"result":{"protocolVersion":"2025-06-18"}}      │                │
      │                │                │                │                │
      │ ═══════════════ 2. 工具发现阶段 ═══════════════════════════════════ │
      │                │                │                │                │
      ├───────────────→│                │                │                │
      │ POST /mcp/tools/list            │                │                │
      │                │                │                │                │
      │                ├───────────────→│                │                │
      │                │ 扫描注册的工具   │                │                │
      │                │                │                │                │
      │                │                │                │                │ 
      │                │                │                │                │
      │                │                │                │                │
      │                │←───────────────┤                │                │
      │                │ 返回工具列表和Schema              │                │
      │                │                │                │                │
      │←───────────────┤                │                │                │
      │ {"result":{"tools":[...]}}      │                │                │
      │                │                │                │                │
      │ ═══════════════ 3. 工具调用阶段 ═══════════════════════════════════ │
      │                │                │                │                │
      ├───────────────→│                │                │                │
      │ POST /mcp/tools/call            │                │                │
      │ {"method":"tools/call"}         │                │                │
      │                │                │                │                │
      │                ├───────────────→│                │                │
      │                │ 验证权限 + 参数校验                │                │
      │                │                │                │                │
      │                │                ├───────────────→│                │
      │                │                │ 调用对应业务方法                   │
      │                │                │                │                │
      │                │                │                ├───────────────→│
      │                │                │                │ 执行业务逻辑    │
      │                │                │                │                │
      │                │                │                │←───────────────┤
      │                │                │                │ 返回业务结果    │
      │                │                │                │                │
      │                │                │←───────────────┤                │
      │                │                │ 格式化响应数据   │                │
      │                │                │                │                │
      │                │                ├────────────────┤                │
      │                │                │ 记录审计日志     │                │
      │                │                │                │                │
      │                │←───────────────┤                │                │
      │                │ 返回执行结果     │                │                │
      │                │                │                │                │
      │←───────────────┤                │                │                │
      │ {"result":{"content":[...],"isError":false}}     │                │
      │                │                │                │                │
```

## 技术栈架构

### 技术组件分层图

#### ASCII 架构图（IDEA 完全兼容）

```
                    🖥️ 前端技术栈
    ┌─────────────────────────────────────────────────┐
    │  Claude Desktop  │  Cursor IDE  │  通义 Lingma   │
    │      v1.0+       │    v0.40+    │     最新版      │
    └─────────────────────────────────────────────────┘
                              │
                              ▼
                    🏗️ 应用框架层
    ┌─────────────────────────────────────────────────┐
    │    Java 21 LTS   │ Spring Boot  │ Spring Security │
    │     OpenJDK      │    3.3.5     │      6.x        │
    ├─────────────────────────────────────────────────┤
    │   Spring AOP     │    Spring    │   Maven 3.9+   │
    │   面向切面编程    │   Actuator   │   构建工具      │
    └─────────────────────────────────────────────────┘
                              │
                              ▼
                    ⚙️ 运行时环境
    ┌─────────────────────────────────────────────────┐
    │   Docker 24.0+   │ Kubernetes   │     Nginx      │
    │     容器化       │   1.28+      │   反向代理      │
    ├─────────────────────────────────────────────────┤
    │    HAProxy       │   JVM 优化   │   SSL/TLS      │
    │   负载均衡       │   G1GC       │    加密        │
    └─────────────────────────────────────────────────┘
                              │
                              ▼
                    🗄️ 数据存储层
    ┌─────────────────────────────────────────────────┐
    │   MySQL 8.0+     │  Redis 7.0+  │   InfluxDB     │
    │   关系数据库      │   缓存数据库  │  时序数据库     │
    ├─────────────────────────────────────────────────┤
    │ Elasticsearch    │   HikariCP   │   数据备份      │
    │   搜索引擎       │   连接池     │   容灾恢复      │
    └─────────────────────────────────────────────────┘
                              │
                              ▼
                    🏗️ 基础设施层
    ┌─────────────────────────────────────────────────┐
    │  Apollo 2.3.0    │ Prometheus   │   ELK Stack    │
    │    配置中心      │   监控系统    │   日志系统      │
    ├─────────────────────────────────────────────────┤
    │    Grafana       │ AlertManager │   Jaeger       │
    │   可视化面板      │   告警系统    │   链路追踪      │
    └─────────────────────────────────────────────────┘
```

#### 技术栈依赖关系

```
AI 客户端 ──→ HTTP/HTTPS ──→ 网关层 ──→ MCP 服务
    │                                      │
    │                                      ▼
    └──→ WebSocket ──→ 实时通信 ──→ 业务服务层
                                          │
                                          ▼
                                    数据存储层
                                          │
                                          ▼
                                    基础设施层
```

### 技术选型说明

| 层级 | 技术组件 | 版本 | 选型理由 |
|------|----------|------|----------|
| **语言运行时** | Java | 21 LTS | 长期支持版本，性能优化，现代语法特性 |
| **应用框架** | Spring Boot | 3.3.5 | 企业级框架，生态完善，快速开发 |
| **安全框架** | Spring Security | 6.x | 成熟的安全解决方案，OAuth2/JWT 支持 |
| **配置管理** | Apollo | 2.3.0 | 携程开源，动态配置，多环境支持 |
| **容器化** | Docker | 24.0+ | 标准化部署，环境一致性 |
| **编排平台** | Kubernetes | 1.28+ | 云原生，自动扩缩容，服务发现 |
| **数据库** | MySQL | 8.0+ | 成熟稳定，ACID 事务，丰富生态 |
| **缓存** | Redis | 7.0+ | 高性能，数据结构丰富，持久化 |
| **监控** | Prometheus | 最新 | 云原生监控，时序数据，告警规则 |
| **日志** | ELK Stack | 最新 | 日志收集分析，全文搜索，可视化 |

## 核心组件设计

### 1. MCP 协议处理器

**职责:**
- MCP 协议解析和验证
- JSON-RPC 2.0 消息处理
- 工具调用路由和分发

**关键类:**
- `McpController`: HTTP 请求处理
- `McpRequest/McpResponse`: 协议模型
- `McpToolService`: 工具管理

### 2. 安全认证模块

**职责:**
- API Key 验证
- JWT 令牌处理
- 权限检查和授权

**关键类:**
- `ApiKeyAuthenticationFilter`: API Key 过滤器
- `SecurityConfig`: 安全配置
- `AuthenticationEntryPointImpl`: 认证入口

### 3. 业务服务适配器

**职责:**
- 内部服务调用封装
- 数据格式转换
- 异常处理和重试

**关键类:**
- `BusinessService`: 业务逻辑封装
- `UserService`: 用户相关操作
- `OrderService`: 订单相关操作

## 数据流设计

### 1. 请求处理流程

```
Client Request
      │
      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Security  │───▶│   MCP       │───▶│  Business   │
│   Filter    │    │  Controller │    │   Service   │
└─────────────┘    └─────────────┘    └─────────────┘
      │                    │                    │
      ▼                    ▼                    ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Auth      │    │   Tool      │    │   Internal  │
│   Check     │    │   Routing   │    │   System    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 2. 错误处理流程

```
Exception Occurred
      │
      ▼
┌─────────────────┐
│  Global         │
│  Exception      │
│  Handler        │
└─────────────────┘
      │
      ▼
┌─────────────────┐    ┌─────────────────┐
│  Error          │───▶│  Audit          │
│  Response       │    │  Logging        │
│  Formatting     │    │                 │
└─────────────────┘    └─────────────────┘
```

## 安全设计

### 1. 认证授权流程

```
1. Client Request with API Key
2. API Key Validation
3. Permission Check
4. Tool Access Control
5. Data Level Security
6. Response Filtering
```

### 2. 数据安全

**敏感数据处理:**
- 输入数据验证和清理
- 输出数据脱敏处理
- 日志中敏感信息过滤

**访问控制:**
- 基于角色的权限控制
- 行级数据安全
- 字段级访问控制

## 性能设计

### 1. 缓存策略

**多级缓存:**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   L1 Cache  │───▶│   L2 Cache  │───▶│  Database   │
│  (In-Memory)│    │   (Redis)   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
```

**缓存配置:**
- 工具定义缓存: 1小时
- 用户信息缓存: 15分钟
- 权限信息缓存: 30分钟

### 2. 连接池配置

**数据库连接池:**
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
```

**HTTP 连接池:**
```yaml
http:
  client:
    max-connections: 100
    max-connections-per-route: 20
    connection-timeout: 5000
    read-timeout: 30000
```

## 监控设计

### 1. 指标收集

**业务指标:**
- API 调用次数和成功率
- 工具使用统计
- 响应时间分布
- 错误率和错误类型

**系统指标:**
- CPU 和内存使用率
- JVM 垃圾回收情况
- 数据库连接池状态
- 线程池使用情况

### 2. 日志设计

**日志级别:**
- ERROR: 系统错误和异常
- WARN: 安全事件和异常访问
- INFO: 业务操作和状态变更
- DEBUG: 详细的调试信息

**日志格式:**
```json
{
  "timestamp": "2024-07-21T10:30:00.000Z",
  "level": "INFO",
  "logger": "com.wanshifu.mcp.controller.McpController",
  "message": "Tool call executed successfully",
  "mdc": {
    "traceId": "abc123",
    "clientId": "client001",
    "toolName": "query_user"
  }
}
```

## 扩展性设计

### 1. 水平扩展

**无状态设计:**
- 所有状态存储在外部系统
- 支持多实例部署
- 负载均衡友好

**服务发现:**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/
```

### 2. 垂直扩展

**模块化设计:**
- 工具插件化
- 业务服务解耦
- 配置外部化

**动态配置:**
```yaml
management:
  endpoints:
    web:
      exposure:
        include: refresh,health,info
```

## 部署架构

### 1. 容器化部署

**Docker 镜像分层:**
```
┌─────────────────┐
│   Application   │  ← 应用层 (最小变更)
├─────────────────┤
│   Dependencies  │  ← 依赖层 (较少变更)
├─────────────────┤
│   JRE Runtime   │  ← 运行时层 (稳定)
├─────────────────┤
│   Base OS       │  ← 基础层 (稳定)
└─────────────────┘
```

### 2. 微服务架构

**服务拆分:**
- MCP Gateway Service
- Authentication Service
- Tool Management Service
- Business Adapter Service

**服务通信:**
- 同步调用: HTTP/REST
- 异步消息: RabbitMQ/Kafka
- 服务发现: Eureka/Consul

## 灾难恢复

### 1. 高可用设计

**多活部署:**
```
┌─────────────┐    ┌─────────────┐
│   Region A  │    │   Region B  │
│             │    │             │
│  ┌───────┐  │    │  ┌───────┐  │
│  │ MCP-1 │  │    │  │ MCP-3 │  │
│  └───────┘  │    │  └───────┘  │
│  ┌───────┐  │    │  ┌───────┐  │
│  │ MCP-2 │  │    │  │ MCP-4 │  │
│  └───────┘  │    │  └───────┘  │
└─────────────┘    └─────────────┘
```

### 2. 数据备份

**备份策略:**
- 实时数据同步
- 定期全量备份
- 增量备份
- 跨区域备份

## 技术选型

### 1. 核心技术栈

| 组件 | 技术选择 | 版本 | 说明 |
|------|----------|------|------|
| 框架 | Spring Boot | 2.7.18 | 企业级Java框架 |
| 安全 | Spring Security | 5.7.x | 安全认证框架 |
| 序列化 | Jackson | 2.15.2 | JSON处理 |
| 数据库 | MySQL | 8.0+ | 关系型数据库 |
| 缓存 | Redis | 6.0+ | 内存数据库 |
| 消息队列 | RabbitMQ | 3.8+ | 消息中间件 |

### 2. 运维技术栈

| 组件 | 技术选择 | 说明 |
|------|----------|------|
| 容器化 | Docker | 应用容器化 |
| 编排 | Kubernetes | 容器编排 |
| 监控 | Prometheus + Grafana | 指标监控 |
| 日志 | ELK Stack | 日志收集分析 |
| 链路追踪 | Jaeger | 分布式追踪 |
| CI/CD | Jenkins | 持续集成部署 |

这个架构设计确保了系统的安全性、可扩展性和高可用性，为万师傅的MCP服务提供了坚实的技术基础。

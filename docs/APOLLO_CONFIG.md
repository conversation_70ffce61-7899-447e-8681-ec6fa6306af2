# Apollo配置中心集成指南

本文档说明如何在万师傅MCP服务中集成和使用Apollo配置中心。

## 🚀 功能特性

### 1. 动态配置管理
- 实时配置更新，无需重启服务
- 配置版本管理和回滚
- 多环境配置隔离

### 2. 安全配置
- API密钥集中管理
- 敏感信息加密存储
- 权限控制和审计

### 3. 配置监控
- 配置变更通知
- 配置使用情况监控
- 异常配置告警

## 📝 配置说明

### 1. Apollo服务端配置

在Apollo配置中心创建应用：
- **AppId**: `mcp-service-demo`
- **环境**: `DEV`, `TEST`, `PROD`
- **集群**: `default`

### 2. 配置项说明

#### 应用配置 (application namespace)

```properties
# MCP服务配置
mcp.protocol-version=2025-06-18

# 安全配置
mcp.security.api-keys[0]=demo-api-key-123456
mcp.security.api-keys[1]=wanshifu-api-key-789012
mcp.security.api-keys[2]=prod-api-key-abcdef

# 工具配置
mcp.tools.enabled=true
mcp.tools.auto-scan=true
mcp.tools.base-packages=com.wanshifu.mcp.tools
mcp.tools.rate-limit.enabled=true
mcp.tools.rate-limit.requests-per-minute=100

# 日志配置
logging.level.com.wanshifu.mcp=INFO
logging.level.org.springframework.security=WARN
```

#### 数据库配置 (database namespace)

```properties
# 数据库连接配置（如果需要）
spring.datasource.url=***************************************
spring.datasource.username=mcp_user
spring.datasource.password=encrypted_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
```

### 3. 环境变量配置

```bash
# Apollo配置
export APOLLO_META_SERVER=http://apollo-config-server:8080
export APOLLO_ENV=PROD
export APOLLO_CLUSTER=default
export APOLLO_APP_ID=mcp-service-demo

# 应用配置
export SPRING_PROFILES_ACTIVE=prod
```

### 4. Docker环境配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  mcp-service:
    image: mcp-service-demo:latest
    environment:
      - APOLLO_META_SERVER=http://apollo-config-server:8080
      - APOLLO_ENV=PROD
      - APOLLO_CLUSTER=default
      - APOLLO_APP_ID=mcp-service-demo
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - apollo-config-server
```

## 🔧 使用方式

### 1. 配置属性注入

```java
@Component
@ConfigurationProperties(prefix = "mcp")
public class McpProperties {
    // 配置属性会自动从Apollo获取并注入
}
```

### 2. 动态配置监听

```java
@Component
public class ConfigChangeListener {
    
    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            logger.info("配置变更: {} = {} -> {}", 
                key, change.getOldValue(), change.getNewValue());
        }
    }
}
```

### 3. 配置值获取

```java
@Component
public class ConfigService {
    
    @Value("${mcp.protocol-version}")
    private String protocolVersion;
    
    @ApolloConfig
    private Config config;
    
    public String getApiKey() {
        return config.getProperty("mcp.security.api-keys[0]", "default-key");
    }
}
```

## 🛠️ 最佳实践

### 1. 配置分层
- **公共配置**: 放在 `application` namespace
- **环境配置**: 使用不同的环境（DEV/TEST/PROD）
- **业务配置**: 创建专门的 namespace

### 2. 安全配置
- 敏感信息使用加密配置
- 定期轮换API密钥
- 配置访问权限控制

### 3. 监控告警
- 配置变更通知
- 配置获取失败告警
- 配置使用情况监控

## 🚨 故障处理

### 1. Apollo服务不可用
- 应用会使用本地缓存配置
- 配置文件备份机制
- 降级到默认配置

### 2. 配置获取失败
- 检查网络连接
- 验证Apollo服务状态
- 查看应用日志

### 3. 配置更新不生效
- 检查配置发布状态
- 验证应用订阅情况
- 重启应用服务

## 📚 参考资料

- [Apollo官方文档](https://www.apolloconfig.com/)
- [Spring Boot集成Apollo](https://github.com/apolloconfig/apollo-use-cases/tree/master/spring-boot-apollo)
- [Apollo最佳实践](https://www.apolloconfig.com/#/zh/usage/best-practices-recommendation)

# 安全指南

万师傅 MCP 服务采用企业级多层安全防护架构，确保系统在各个层面都有相应的安全措施，保护业务数据和系统安全。

## 概述

本文档详细说明万师傅 MCP 服务的安全架构、认证机制、防护措施和最佳实践，帮助开发者和运维人员构建安全可靠的 MCP 服务。

## 安全架构

### 1. 多层安全防护

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI 客户端     │    │   MCP 网关      │    │   内部业务系统   │
│                 │    │                 │    │                 │
│ - API Key       │───▶│ - 认证授权      │───▶│ - 业务逻辑      │
│ - 请求签名      │    │ - 速率限制      │    │ - 数据访问      │
│ - TLS 加密      │    │ - 请求验证      │    │ - 权限控制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 安全组件

- **认证层**: API Key + JWT + 请求签名
- **授权层**: 基于角色的访问控制 (RBAC)
- **网络层**: TLS 加密 + IP 白名单
- **应用层**: 输入验证 + 输出过滤
- **监控层**: 审计日志 + 异常检测

## 认证机制

### 1. API Key 认证

**配置示例:**
```yaml
mcp:
  security:
    api-keys: 
      - key: "wanshifu-prod-key-001"
        name: "生产环境主密钥"
        permissions: ["user:read", "order:read", "order:update"]
        expires: "2024-12-31"
      - key: "wanshifu-dev-key-001"
        name: "开发环境密钥"
        permissions: ["user:read", "order:read"]
        expires: "2024-06-30"
```

**使用方式:**
```bash
# 方式1: X-API-Key 头部
curl -H "X-API-Key: wanshifu-prod-key-001" ...

# 方式2: Authorization Bearer
curl -H "Authorization: Bearer wanshifu-prod-key-001" ...
```

### 2. JWT 令牌认证

**生成JWT:**
```java
@Service
public class JwtService {
    
    public String generateToken(String clientId, List<String> permissions) {
        return Jwts.builder()
            .setSubject(clientId)
            .claim("permissions", permissions)
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + 3600000)) // 1小时
            .signWith(SignatureAlgorithm.HS256, secretKey)
            .compact();
    }
}
```

### 3. 请求签名验证

**签名算法:**
```java
public class RequestSigner {
    
    public String signRequest(String method, String uri, String body, String timestamp, String apiKey) {
        String stringToSign = method + "\n" + uri + "\n" + body + "\n" + timestamp;
        return hmacSha256(stringToSign, apiKey);
    }
}
```

## 权限控制

### 1. 基于角色的访问控制 (RBAC)

**权限定义:**
```java
public enum Permission {
    USER_READ("user:read", "查询用户信息"),
    USER_WRITE("user:write", "修改用户信息"),
    ORDER_READ("order:read", "查询订单信息"),
    ORDER_WRITE("order:write", "修改订单信息"),
    NOTIFICATION_SEND("notification:send", "发送通知");
    
    private final String code;
    private final String description;
}
```

**权限检查:**
```java
@PreAuthorize("hasPermission('user:read')")
public Map<String, Object> queryUser(Map<String, Object> params) {
    // 业务逻辑
}
```

### 2. 数据访问控制

**行级安全:**
```java
@Service
public class DataAccessService {
    
    public List<Order> getOrdersByUser(String userId, String requestUserId) {
        // 只能访问自己的订单数据
        if (!userId.equals(requestUserId) && !hasAdminRole()) {
            throw new AccessDeniedException("无权访问其他用户订单");
        }
        return orderRepository.findByUserId(userId);
    }
}
```

## 网络安全

### 1. TLS 配置

**Nginx 配置:**
```nginx
server {
    listen 443 ssl http2;
    server_name mcp.wanshifu.com;
    
    ssl_certificate /etc/ssl/certs/wanshifu.crt;
    ssl_certificate_key /etc/ssl/private/wanshifu.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;
    
    location /mcp/ {
        proxy_pass http://mcp-service:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. IP 白名单

**Spring Security 配置:**
```java
@Configuration
public class IpWhitelistConfig {
    
    @Value("${mcp.security.allowed-ips}")
    private List<String> allowedIps;
    
    @Bean
    public IpAddressFilter ipAddressFilter() {
        return new IpAddressFilter(allowedIps);
    }
}
```

## 输入验证

### 1. 参数验证

**验证注解:**
```java
public class UserQueryRequest {
    
    @NotBlank(message = "用户ID不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{6,20}$", message = "用户ID格式不正确")
    private String userId;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
```

### 2. SQL 注入防护

**使用参数化查询:**
```java
@Repository
public class UserRepository {
    
    @Query("SELECT u FROM User u WHERE u.userId = :userId")
    User findByUserId(@Param("userId") String userId);
}
```

## 输出过滤

### 1. 敏感信息过滤

**数据脱敏:**
```java
@JsonSerialize(using = SensitiveDataSerializer.class)
public class UserInfo {
    private String userId;
    
    @SensitiveData(type = SensitiveType.PHONE)
    private String phone;
    
    @SensitiveData(type = SensitiveType.EMAIL)
    private String email;
}
```

### 2. 错误信息过滤

**统一异常处理:**
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<McpResponse> handleException(Exception e) {
        // 不暴露内部错误信息
        String message = isProductionEnvironment() ? "系统内部错误" : e.getMessage();
        return ResponseEntity.ok(McpResponse.error(null, -32603, message));
    }
}
```

## 速率限制

### 1. 基于令牌桶算法

**配置示例:**
```java
@Component
public class RateLimitFilter {
    
    private final Map<String, Bucket> buckets = new ConcurrentHashMap<>();
    
    public boolean isAllowed(String apiKey) {
        Bucket bucket = buckets.computeIfAbsent(apiKey, k -> 
            Bucket4j.builder()
                .addLimit(Bandwidth.classic(100, Refill.intervally(100, Duration.ofMinutes(1))))
                .build()
        );
        
        return bucket.tryConsume(1);
    }
}
```

### 2. 分级限流

**不同客户端不同限制:**
```yaml
mcp:
  rate-limit:
    default: 100  # 每分钟100次
    premium: 1000 # 高级客户端每分钟1000次
    internal: -1  # 内部调用不限制
```

## 审计日志

### 1. 访问日志

**日志格式:**
```json
{
  "timestamp": "2024-07-21T10:30:00.000Z",
  "clientId": "wanshifu-client-001",
  "apiKey": "***-key-001",
  "method": "tools/call",
  "toolName": "query_user",
  "parameters": {"userId": "user001"},
  "responseTime": 150,
  "status": "success",
  "clientIp": "*************",
  "userAgent": "MCP-Client/1.0"
}
```

### 2. 安全事件日志

**异常检测:**
```java
@Component
public class SecurityEventLogger {
    
    public void logSuspiciousActivity(String apiKey, String event, String details) {
        SecurityEvent event = SecurityEvent.builder()
            .timestamp(Instant.now())
            .apiKey(maskApiKey(apiKey))
            .eventType(event)
            .details(details)
            .severity(SecurityLevel.HIGH)
            .build();
            
        securityEventRepository.save(event);
        
        // 触发告警
        if (isHighRiskEvent(event)) {
            alertService.sendAlert(event);
        }
    }
}
```

## 监控告警

### 1. 关键指标监控

**监控指标:**
- API 调用频率和成功率
- 响应时间分布
- 错误率和错误类型
- 异常IP访问
- 可疑请求模式

### 2. 告警规则

**告警配置:**
```yaml
alerts:
  - name: "API调用异常"
    condition: "error_rate > 5%"
    duration: "5m"
    action: "send_email"
    
  - name: "可疑IP访问"
    condition: "failed_auth_count > 10"
    duration: "1m"
    action: "block_ip"
```

## 部署安全

### 1. 容器安全

**Dockerfile 最佳实践:**
```dockerfile
# 使用非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser

# 最小化镜像
FROM openjdk:8-jre-alpine

# 安全扫描
RUN apk add --no-cache dumb-init
ENTRYPOINT ["dumb-init", "--"]
```

### 2. 环境隔离

**网络隔离:**
```yaml
# docker-compose.yml
networks:
  mcp-internal:
    driver: bridge
    internal: true
  mcp-external:
    driver: bridge
```

## 应急响应

### 1. 安全事件响应流程

1. **检测**: 自动监控系统检测异常
2. **分析**: 安全团队分析事件严重程度
3. **响应**: 根据预案执行应急措施
4. **恢复**: 修复漏洞并恢复服务
5. **总结**: 事后分析和改进

### 2. 应急措施

**API Key 紧急撤销:**
```bash
# 撤销特定API Key
curl -X POST http://admin.wanshifu.com/api/revoke-key \
  -H "Authorization: Bearer admin-token" \
  -d '{"apiKey": "compromised-key"}'

# 临时禁用所有外部访问
kubectl patch deployment mcp-service -p '{"spec":{"replicas":0}}'
```

## 合规要求

### 1. 数据保护

- 遵循GDPR/个人信息保护法
- 数据最小化原则
- 数据加密存储和传输
- 定期数据清理

### 2. 审计要求

- 完整的访问日志
- 数据变更记录
- 权限变更审批
- 定期安全评估

## 最佳实践总结

1. **最小权限原则**: 只授予必要的最小权限
2. **深度防御**: 多层安全防护机制
3. **持续监控**: 实时监控和告警
4. **定期评估**: 定期安全评估和渗透测试
5. **应急准备**: 完善的应急响应预案
6. **人员培训**: 定期安全培训和意识提升

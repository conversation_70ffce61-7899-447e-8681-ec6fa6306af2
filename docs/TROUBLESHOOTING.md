# 故障排查指南

本文档提供万师傅 MCP 服务常见问题的诊断和解决方案。

## 快速诊断

### 健康检查清单

在开始详细排查之前，请先执行以下基础检查：

```bash
# 1. 服务健康状态
curl -f http://localhost:8080/actuator/health

# 2. 服务版本信息
curl http://localhost:8080/actuator/info

# 3. MCP 协议连通性
curl -X POST http://localhost:8080/mcp/initialize \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{"jsonrpc":"2.0","method":"initialize","id":"1"}'

# 4. 工具列表获取
curl -X POST http://localhost:8080/mcp/tools/list \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{"jsonrpc":"2.0","method":"tools/list","id":"2"}'
```

## 常见问题分类

### 🚀 启动问题

#### 问题 1: 服务启动失败

**症状**:
```
Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
```

**可能原因**:
- 端口被占用
- 配置文件错误
- 依赖冲突
- JDK 版本不兼容

**解决步骤**:

1. **检查端口占用**:
```bash
# 检查 8080 端口
netstat -tlnp | grep 8080
# 或使用 lsof
lsof -i :8080
```

2. **检查 JDK 版本**:
```bash
java -version
# 确保是 Java 21+
```

3. **启用调试模式**:
```bash
java -jar app.jar --debug
# 或设置环境变量
export SPRING_PROFILES_ACTIVE=dev
java -jar app.jar
```

4. **检查配置文件**:
```bash
# 验证 YAML 语法
python -c "import yaml; yaml.safe_load(open('application.yml'))"
```

#### 问题 2: Apollo 配置中心连接失败

**症状**:
```
Could not resolve placeholder 'apollo.meta' in value "${apollo.meta}"
```

**解决方案**:
```bash
# 设置 Apollo 环境变量
export APOLLO_META_SERVER=http://apollo-server:8080
export APOLLO_ENV=PROD
export APOLLO_APP_ID=mcp-service-demo

# 或在配置文件中禁用 Apollo
apollo:
  bootstrap:
    enabled: false
```

### 🔐 认证问题

#### 问题 3: API Key 认证失败

**症状**:
```json
{
  "error": {
    "code": -32001,
    "message": "Unauthorized: Invalid or missing API key"
  }
}
```

**解决步骤**:

1. **检查 API Key 配置**:
```yaml
# application.yml
mcp:
  security:
    api-keys:
      - demo-api-key-123456
      - your-api-key-here
```

2. **验证请求头格式**:
```bash
# 正确的请求头格式
curl -H "X-API-Key: demo-api-key-123456" ...
# 或
curl -H "Authorization: Bearer demo-api-key-123456" ...
```

3. **检查 API Key 是否生效**:
```bash
# 查看当前配置
curl http://localhost:8080/actuator/configprops | grep -i api-key
```

#### 问题 4: 请求被拒绝 (403 Forbidden)

**症状**:
```
HTTP/1.1 403 Forbidden
```

**可能原因**:
- IP 不在白名单中
- API Key 权限不足
- 请求频率超限

**解决方案**:
```yaml
# 检查 IP 白名单配置
mcp:
  security:
    ip-whitelist:
      - ***********/24
      - 10.0.0.0/8
    rate-limit:
      requests-per-minute: 100
```

### 🛠️ 工具调用问题

#### 问题 5: 工具未找到

**症状**:
```json
{
  "error": {
    "code": -32601,
    "message": "Method not found: unknown_tool"
  }
}
```

**解决步骤**:

1. **检查工具是否注册**:
```bash
# 查看启动日志
grep "MCP工具扫描完成" logs/application.log
```

2. **验证工具注解**:
```java
@McpTool(
    name = "query_user",  // 确保名称正确
    description = "查询用户信息"
)
public Map<String, Object> queryUser(Map<String, Object> arguments) {
    // 实现逻辑
}
```

3. **检查包扫描路径**:
```yaml
mcp:
  tools:
    base-packages: com.wanshifu.mcp.tools
```

#### 问题 6: 参数验证失败

**症状**:
```json
{
  "error": {
    "code": -32602,
    "message": "Invalid params"
  }
}
```

**解决方案**:
1. 检查参数格式是否符合 JSON Schema
2. 验证必需参数是否提供
3. 确认参数类型是否正确

### 🌐 网络连接问题

#### 问题 7: 连接超时

**症状**:
```
Connection timed out
```

**排查步骤**:

1. **检查网络连通性**:
```bash
# 测试服务器连通性
ping your-mcp-server.com

# 测试端口连通性
telnet your-mcp-server.com 8080
```

2. **检查防火墙设置**:
```bash
# 检查防火墙状态
sudo ufw status

# 开放端口
sudo ufw allow 8080/tcp
```

3. **检查 DNS 解析**:
```bash
nslookup your-mcp-server.com
```

#### 问题 8: SSL/TLS 证书问题

**症状**:
```
SSL certificate problem: unable to get local issuer certificate
```

**解决方案**:
```bash
# 临时跳过证书验证（仅用于测试）
curl -k https://your-mcp-server.com/mcp/initialize

# 更新证书存储
sudo apt-get update && sudo apt-get install ca-certificates

# 检查证书有效性
openssl s_client -connect your-mcp-server.com:443
```

### 📊 性能问题

#### 问题 9: 响应时间过长

**症状**:
- API 响应时间超过 5 秒
- 客户端超时错误

**优化方案**:

1. **检查 JVM 参数**:
```bash
# 优化 JVM 设置
java -Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar app.jar
```

2. **启用缓存**:
```yaml
spring:
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379
```

3. **数据库连接池优化**:
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

#### 问题 10: 内存不足

**症状**:
```
java.lang.OutOfMemoryError: Java heap space
```

**解决方案**:
```bash
# 增加堆内存
java -Xms2g -Xmx4g -jar app.jar

# 启用内存分析
java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp -jar app.jar

# 监控内存使用
curl http://localhost:8080/actuator/metrics/jvm.memory.used
```

## 日志分析

### 关键日志位置

```bash
# 应用日志
tail -f logs/application.log

# Spring Boot 启动日志
tail -f logs/spring.log

# 访问日志
tail -f logs/access.log

# 错误日志
tail -f logs/error.log
```

### 常见错误日志模式

| 错误模式 | 含义 | 解决方案 |
|----------|------|----------|
| `ConnectException` | 连接被拒绝 | 检查目标服务状态 |
| `SocketTimeoutException` | 连接超时 | 增加超时时间或检查网络 |
| `ClassNotFoundException` | 类未找到 | 检查依赖和类路径 |
| `BeanCreationException` | Bean 创建失败 | 检查配置和依赖注入 |
| `DataAccessException` | 数据访问异常 | 检查数据库连接和 SQL |

### 日志级别调整

```yaml
# 临时启用调试日志
logging:
  level:
    com.wanshifu.mcp: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.springframework.boot.autoconfigure: DEBUG
```

## 监控和诊断工具

### 内置监控端点

```bash
# 健康检查
curl http://localhost:8080/actuator/health

# 应用信息
curl http://localhost:8080/actuator/info

# JVM 指标
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# HTTP 请求指标
curl http://localhost:8080/actuator/metrics/http.server.requests

# 线程信息
curl http://localhost:8080/actuator/threaddump

# 堆转储
curl http://localhost:8080/actuator/heapdump -o heapdump.hprof
```

### 外部监控工具

1. **Prometheus + Grafana**:
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'mcp-service'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
```

2. **ELK Stack**:
```yaml
# logstash.conf
input {
  file {
    path => "/path/to/logs/application.log"
    type => "mcp-service"
  }
}
```

## 应急处理

### 服务重启

```bash
# 优雅重启
curl -X POST http://localhost:8080/actuator/shutdown

# 强制重启
sudo systemctl restart mcp-service

# Docker 重启
docker restart mcp-service
```

### 回滚操作

```bash
# Git 回滚
git checkout previous-stable-tag

# Docker 回滚
docker run -d --name mcp-service-rollback previous-image:tag

# Kubernetes 回滚
kubectl rollout undo deployment/mcp-service
```

### 紧急配置修改

```bash
# 通过 Apollo 修改配置
# 1. 登录 Apollo 控制台
# 2. 修改配置项
# 3. 发布配置

# 通过环境变量覆盖
export MCP_SECURITY_API_KEYS="emergency-key-123"
sudo systemctl restart mcp-service
```

## 获取技术支持

### 收集诊断信息

在联系技术支持前，请收集以下信息：

```bash
#!/bin/bash
# 诊断信息收集脚本

echo "=== 系统信息 ===" > diagnostic.log
uname -a >> diagnostic.log
java -version >> diagnostic.log 2>&1

echo "=== 服务状态 ===" >> diagnostic.log
curl -s http://localhost:8080/actuator/health >> diagnostic.log

echo "=== 配置信息 ===" >> diagnostic.log
curl -s http://localhost:8080/actuator/configprops >> diagnostic.log

echo "=== 最近日志 ===" >> diagnostic.log
tail -n 100 logs/application.log >> diagnostic.log

echo "=== JVM 信息 ===" >> diagnostic.log
curl -s http://localhost:8080/actuator/metrics/jvm.memory.used >> diagnostic.log

echo "诊断信息已保存到 diagnostic.log"
```

### 联系方式

- **GitHub Issues**: [报告问题](https://github.com/wanshifu/mcp-service-demo/issues)
- **技术支持邮箱**: <EMAIL>
- **紧急热线**: 400-xxx-xxxx（工作时间：9:00-18:00）

### 问题报告模板

```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- 操作系统：
- Java 版本：
- 服务版本：
- 部署方式：

## 重现步骤
1. 
2. 
3. 

## 期望结果
描述期望的正常行为

## 实际结果
描述实际发生的情况

## 错误日志
```
粘贴相关错误日志
```

## 已尝试的解决方案
列出已经尝试过的解决方法
```

---

💡 **提示**: 大多数问题都可以通过查看日志和检查配置来解决。如果问题持续存在，请不要犹豫联系我们的技术支持团队。

# API 参考文档

万师傅 MCP 服务完全遵循 [MCP 2025-06-18 协议规范](https://modelcontextprotocol.io/)，提供标准化的 JSON-RPC 2.0 接口。

## 基础信息

- **协议版本**: MCP 2025-06-18
- **传输协议**: HTTP/HTTPS
- **数据格式**: JSON-RPC 2.0
- **认证方式**: API Key
- **基础URL**: `http://openapi.wanshifu.com/mcp`

## 认证

所有 API 请求都需要包含有效的 API Key，支持两种认证方式：

### 方式一：X-API-Key 请求头

```http
POST /mcp/initialize HTTP/1.1
Host: localhost:8080
Content-Type: application/json
X-API-Key: your-api-key-here

{request body}
```

### 方式二：Authorization Bearer

```http
POST /mcp/initialize HTTP/1.1
Host: localhost:8080
Content-Type: application/json
Authorization: Bearer your-api-key-here

{request body}
```

## 核心 API

### 1. 初始化连接

建立 MCP 连接并协商协议版本和能力。

**端点**: `POST /mcp/initialize`

**请求格式**:
```json
{
  "jsonrpc": "2.0",
  "method": "initialize",
  "params": {
    "protocolVersion": "2025-06-18",
    "capabilities": {
      "roots": {"listChanged": true},
      "sampling": {},
      "elicitation": {}
    },
    "clientInfo": {
      "name": "YourClient",
      "version": "1.0.0"
    }
  },
  "id": "1"
}
```

**响应格式**:
```json
{
  "jsonrpc": "2.0",
  "result": {
    "protocolVersion": "2025-06-18",
    "capabilities": {
      "tools": {"listChanged": true},
      "resources": {"subscribe": true, "listChanged": true}
    },
    "serverInfo": {
      "name": "万师傅 MCP 服务",
      "version": "1.0.0"
    }
  },
  "id": "1"
}
```

### 2. 获取工具列表

获取所有可用的 MCP 工具及其描述。

**端点**: `POST /mcp/tools/list`

**请求格式**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/list",
  "id": "2"
}
```

**响应格式**:
```json
{
  "jsonrpc": "2.0",
  "result": {
    "tools": [
      {
        "name": "query_user",
        "description": "查询用户信息",
        "inputSchema": {
          "type": "object",
          "properties": {
            "userId": {
              "type": "string",
              "description": "用户ID"
            }
          },
          "required": ["userId"]
        }
      },
      {
        "name": "query_order",
        "description": "查询订单信息",
        "inputSchema": {
          "type": "object",
          "properties": {
            "userId": {
              "type": "string",
              "description": "用户ID"
            },
            "status": {
              "type": "string",
              "description": "订单状态",
              "enum": ["PENDING", "PAID", "SHIPPED", "DELIVERED", "CANCELLED"]
            }
          },
          "required": ["userId"]
        }
      }
    ]
  },
  "id": "2"
}
```

### 3. 调用工具

执行指定的 MCP 工具。

**端点**: `POST /mcp/tools/call`

**请求格式**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "query_user",
    "arguments": {
      "userId": "user001"
    }
  },
  "id": "3"
}
```

**响应格式**:
```json
{
  "jsonrpc": "2.0",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "用户信息查询成功"
      }
    ],
    "isError": false
  },
  "id": "3"
}
```

## 业务工具 API

### 用户管理工具

#### query_user - 查询用户信息

**参数**:
- `userId` (string, 必需): 用户ID

**示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "query_user",
    "arguments": {
      "userId": "user001"
    }
  },
  "id": "1"
}
```

#### create_user - 创建用户

**参数**:
- `username` (string, 必需): 用户名
- `email` (string, 必需): 邮箱地址
- `phone` (string, 可选): 手机号码

**示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "create_user",
    "arguments": {
      "username": "newuser",
      "email": "<EMAIL>",
      "phone": "13800138000"
    }
  },
  "id": "2"
}
```

#### update_user_status - 更新用户状态

**参数**:
- `userId` (string, 必需): 用户ID
- `status` (string, 必需): 新状态 (ACTIVE, INACTIVE, SUSPENDED)
- `reason` (string, 可选): 状态变更原因

### 订单管理工具

#### query_order - 查询订单

**参数**:
- `userId` (string, 必需): 用户ID
- `status` (string, 可选): 订单状态筛选
- `limit` (integer, 可选): 返回数量限制，默认10

**示例**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "query_order",
    "arguments": {
      "userId": "user001",
      "status": "PENDING",
      "limit": 5
    }
  },
  "id": "3"
}
```

#### create_order - 创建订单

**参数**:
- `userId` (string, 必需): 用户ID
- `serviceType` (string, 必需): 服务类型
- `description` (string, 必需): 订单描述
- `amount` (number, 必需): 订单金额

#### update_order_status - 更新订单状态

**参数**:
- `orderId` (string, 必需): 订单ID
- `status` (string, 必需): 新状态
- `reason` (string, 可选): 状态变更原因

#### cancel_order - 取消订单

**参数**:
- `orderId` (string, 必需): 订单ID
- `reason` (string, 必需): 取消原因

### 通知服务工具

#### send_notification - 发送通知

**参数**:
- `userId` (string, 必需): 用户ID
- `type` (string, 必需): 通知类型 (SMS, EMAIL, PUSH)
- `title` (string, 必需): 通知标题
- `content` (string, 必需): 通知内容

#### batch_send_notification - 批量发送通知

**参数**:
- `userIds` (array, 必需): 用户ID列表
- `type` (string, 必需): 通知类型
- `title` (string, 必需): 通知标题
- `content` (string, 必需): 通知内容

#### query_notification_history - 查询通知历史

**参数**:
- `userId` (string, 必需): 用户ID
- `type` (string, 可选): 通知类型筛选
- `limit` (integer, 可选): 返回数量限制

## 错误处理

### 标准错误格式

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32001,
    "message": "Unauthorized: Invalid or missing API key"
  },
  "id": "1"
}
```

### 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| -32700 | Parse error | 检查 JSON 格式 |
| -32600 | Invalid Request | 检查请求结构 |
| -32601 | Method not found | 检查方法名称 |
| -32602 | Invalid params | 检查参数格式 |
| -32603 | Internal error | 服务器内部错误 |
| -32001 | Unauthorized | 检查 API Key |
| -32002 | Forbidden | 权限不足 |
| -32003 | Rate Limited | 请求频率过高 |

### 业务错误

业务逻辑错误会在 `result.content` 中返回，`isError` 字段为 `true`：

```json
{
  "jsonrpc": "2.0",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "用户不存在"
      }
    ],
    "isError": true
  },
  "id": "3"
}
```

## 限流和配额

### 默认限制

- **请求频率**: 100 请求/分钟
- **突发容量**: 20 请求
- **并发连接**: 50 个

### 限流响应

当触发限流时，服务会返回 429 状态码：

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32003,
    "message": "Rate limit exceeded. Try again later."
  },
  "id": "1"
}
```

## 最佳实践

### 1. 错误处理

```javascript
async function callMcpTool(toolName, arguments) {
  try {
    const response = await fetch('/mcp/tools/call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'your-api-key'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: { name: toolName, arguments },
        id: Date.now()
      })
    });
    
    const result = await response.json();
    
    if (result.error) {
      throw new Error(`MCP Error: ${result.error.message}`);
    }
    
    return result.result;
  } catch (error) {
    console.error('MCP call failed:', error);
    throw error;
  }
}
```

### 2. 重试机制

```javascript
async function callWithRetry(toolName, arguments, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await callMcpTool(toolName, arguments);
    } catch (error) {
      if (error.code === -32003 && i < maxRetries - 1) {
        // 限流错误，等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        continue;
      }
      throw error;
    }
  }
}
```

### 3. 批量操作

对于需要处理大量数据的场景，建议使用批量 API 或分页处理：

```javascript
async function batchProcessUsers(userIds) {
  const batchSize = 10;
  const results = [];
  
  for (let i = 0; i < userIds.length; i += batchSize) {
    const batch = userIds.slice(i, i + batchSize);
    const batchResults = await Promise.all(
      batch.map(userId => callMcpTool('query_user', { userId }))
    );
    results.push(...batchResults);
    
    // 避免触发限流
    if (i + batchSize < userIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}
```

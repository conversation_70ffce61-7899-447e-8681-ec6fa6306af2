# 配置指南

本文档详细说明万师傅 MCP 服务的配置选项和最佳实践。

## 配置概览

万师傅 MCP 服务支持多种配置方式，按优先级从高到低排序：

1. **命令行参数** - 启动时指定的参数
2. **环境变量** - 系统环境变量
3. **Apollo 配置中心** - 动态配置管理
4. **配置文件** - application.yml/properties

## 核心配置

### MCP 协议配置

```yaml
mcp:
  # 协议版本（必须与客户端兼容）
  protocol-version: "2025-06-18"
  
  # 安全配置
  security:
    api-keys:
      - demo-api-key-123456
      - wanshifu-api-key-789012
    # JWT 配置（可选）
    jwt:
      secret: your-jwt-secret-key
      expiration: 3600  # 1小时
  
  # 工具配置
  tools:
    enabled: true
    auto-scan: true
    base-packages: com.wanshifu.mcp.tools
    rate-limit:
      enabled: true
      requests-per-minute: 100
      burst-capacity: 20
```

### Spring Boot 配置

```yaml
server:
  port: 8080
  servlet:
    context-path: /
  # SSL 配置（生产环境推荐）
  ssl:
    enabled: false
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12

spring:
  application:
    name: mcp-service-demo
  profiles:
    active: dev
  
  # Jackson 序列化配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
```

### 日志配置

```yaml
logging:
  level:
    com.wanshifu.mcp: INFO
    org.springframework.security: WARN
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mcp-service.log
    max-size: 100MB
    max-history: 30
```

## Apollo 配置中心

### 基础配置

在 `src/main/resources/META-INF/app.properties` 中配置：

```properties
# Apollo 应用配置
app.id=${apollo.app.id:mcp-service-demo}
apollo.meta=${apollo.meta:http://localhost:8080}
apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
```

### 环境变量配置

```bash
# Apollo 服务器地址
export APOLLO_META_SERVER=http://apollo-config-server:8080

# 环境标识
export APOLLO_ENV=PROD

# 应用标识
export APOLLO_APP_ID=mcp-service-demo

# 集群标识
export APOLLO_CLUSTER=default

# 命名空间
export APOLLO_NAMESPACES=application,database,security
```

### Apollo 配置项

#### application 命名空间

```properties
# MCP 服务配置
mcp.protocol-version=2025-06-18
mcp.tools.enabled=true
mcp.tools.auto-scan=true
mcp.tools.base-packages=com.wanshifu.mcp.tools

# 性能配置
mcp.tools.rate-limit.enabled=true
mcp.tools.rate-limit.requests-per-minute=500
mcp.tools.rate-limit.burst-capacity=50

# 日志配置
logging.level.com.wanshifu.mcp=INFO
logging.level.org.springframework.security=WARN
```

#### security 命名空间

```properties
# API 密钥配置
mcp.security.api-keys[0]=prod-api-key-secure-123456
mcp.security.api-keys[1]=backup-api-key-789012
mcp.security.api-keys[2]=emergency-api-key-345678

# JWT 配置
mcp.security.jwt.secret=your-production-jwt-secret-key
mcp.security.jwt.expiration=7200

# IP 白名单（可选）
mcp.security.ip-whitelist[0]=***********/24
mcp.security.ip-whitelist[1]=10.0.0.0/8
```

#### database 命名空间（如果使用数据库）

```properties
# 数据库连接
spring.datasource.url=******************************************************
spring.datasource.username=mcp_user
spring.datasource.password=encrypted_password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
```

## 环境配置

### 开发环境 (dev)

```yaml
# application-dev.yml
mcp:
  security:
    api-keys:
      - demo-api-key-123456
      - dev-test-key-789012
  tools:
    rate-limit:
      requests-per-minute: 1000  # 开发环境放宽限制

logging:
  level:
    com.wanshifu.mcp: DEBUG
    org.springframework.web: DEBUG

# 开发环境可以禁用 Apollo
apollo:
  bootstrap:
    enabled: false
```

### 测试环境 (test)

```yaml
# application-test.yml
mcp:
  security:
    api-keys:
      - test-api-key-123456
      - integration-test-key-789012
  tools:
    rate-limit:
      requests-per-minute: 200

logging:
  level:
    com.wanshifu.mcp: INFO

# 测试环境使用测试 Apollo 服务器
apollo:
  meta: http://apollo-test-server:8080
```

### 生产环境 (prod)

```yaml
# application-prod.yml
mcp:
  security:
    # 生产环境 API 密钥从环境变量或 Apollo 获取
    api-keys: ${MCP_API_KEYS:}
  tools:
    rate-limit:
      requests-per-minute: 100
      burst-capacity: 10

logging:
  level:
    com.wanshifu.mcp: INFO
    org.springframework.security: WARN

# 生产环境必须启用 HTTPS
server:
  ssl:
    enabled: true
    key-store: ${SSL_KEYSTORE_PATH}
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
```

## 配置最佳实践

### 1. 安全配置

- **API 密钥管理**：
  - 使用强随机密钥（至少 32 字符）
  - 定期轮换密钥
  - 不同环境使用不同密钥
  - 敏感配置使用环境变量或加密存储

- **HTTPS 配置**：
  - 生产环境必须启用 HTTPS
  - 使用有效的 SSL 证书
  - 配置安全的 TLS 版本

### 2. 性能配置

- **连接池配置**：
  - 根据并发量调整连接池大小
  - 设置合适的超时时间
  - 监控连接池使用情况

- **限流配置**：
  - 根据系统容量设置限流参数
  - 区分不同客户端的限流策略
  - 实现优雅的限流降级

### 3. 监控配置

- **日志配置**：
  - 生产环境使用 INFO 级别
  - 配置日志轮转和归档
  - 结构化日志便于分析

- **指标配置**：
  - 启用 Actuator 端点
  - 配置自定义业务指标
  - 集成监控系统

## 配置验证

### 启动时验证

服务启动时会自动验证关键配置：

```java
@Component
@Validated
public class ConfigurationValidator {
    
    @Value("${mcp.security.api-keys}")
    @NotEmpty(message = "API keys cannot be empty")
    private List<String> apiKeys;
    
    @PostConstruct
    public void validate() {
        // 验证 API 密钥强度
        apiKeys.forEach(this::validateApiKeyStrength);
    }
}
```

### 运行时验证

```bash
# 检查配置是否生效
curl http://localhost:8080/actuator/configprops

# 检查环境信息
curl http://localhost:8080/actuator/env

# 检查健康状态
curl http://localhost:8080/actuator/health
```

## 故障排查

### 常见配置问题

1. **Apollo 连接失败**
   - 检查网络连接
   - 验证 Apollo 服务器地址
   - 确认应用 ID 和命名空间配置

2. **API 密钥认证失败**
   - 检查密钥配置是否正确
   - 验证请求头格式
   - 查看认证相关日志

3. **端口冲突**
   - 修改服务端口配置
   - 检查端口占用情况
   - 配置负载均衡

### 配置调试

```bash
# 查看当前生效的配置
java -jar app.jar --debug

# 指定配置文件
java -jar app.jar --spring.config.location=classpath:/custom-config.yml

# 覆盖特定配置
java -jar app.jar --mcp.security.api-keys=debug-key-123
```

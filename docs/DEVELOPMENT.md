# 开发指南

本文档为万师傅 MCP 服务的开发者提供详细的开发指导和最佳实践。

## 开发环境搭建

### 必需工具

| 工具 | 版本要求 | 安装方式 |
|------|----------|----------|
| **JDK** | 21+ | [OpenJDK 21](https://openjdk.java.net/projects/jdk/21/) |
| **Maven** | 3.9+ | [Apache Maven](https://maven.apache.org/) |
| **IDE** | 最新版 | IntelliJ IDEA / Eclipse / VS Code |
| **Git** | 2.30+ | [Git SCM](https://git-scm.com/) |

### 推荐工具

- **Docker**: 容器化开发和测试
- **Postman**: API 测试工具
- **JProfiler**: 性能分析工具
- **SonarQube**: 代码质量检查

### 环境配置

#### 1. 克隆项目

```bash
git clone https://github.com/wanshifu/mcp-service-demo.git
cd mcp-service-demo
```

#### 2. IDE 配置

**IntelliJ IDEA**:
```
1. File -> Open -> 选择项目目录
2. 等待 Maven 依赖下载完成
3. 设置 Project SDK 为 Java 21
4. 启用 Annotation Processing
5. 安装推荐插件：Lombok, Spring Boot
```

**VS Code**:
```json
// .vscode/settings.json
{
  "java.configuration.runtimes": [
    {
      "name": "JavaSE-21",
      "path": "/path/to/jdk-21"
    }
  ],
  "java.compile.nullAnalysis.mode": "automatic",
  "spring-boot.ls.problem.application-properties.enabled": true
}
```

#### 3. 本地配置

创建 `application-dev.yml`:

```yaml
# 开发环境配置
server:
  port: 8080

spring:
  profiles:
    active: dev

mcp:
  security:
    api-keys:
      - demo-api-key-123456
      - dev-test-key-789012
  tools:
    enabled: true
    auto-scan: true
    rate-limit:
      enabled: false  # 开发环境禁用限流

logging:
  level:
    com.wanshifu.mcp: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

# 禁用 Apollo（开发环境）
apollo:
  bootstrap:
    enabled: false
```

## 项目结构

```
src/main/java/com/wanshifu/mcp/
├── McpServiceApplication.java          # 主启动类
├── config/                             # 配置类
│   ├── ApolloConfig.java              # Apollo 配置
│   ├── McpProperties.java             # 配置属性
│   └── SecurityConfig.java            # 安全配置
├── controller/                         # 控制器层
│   └── McpController.java             # MCP 协议控制器
├── core/                              # 核心组件
│   ├── ToolRegistry.java              # 工具注册器
│   └── annotation/                    # 注解定义
│       └── McpTool.java               # MCP 工具注解
├── model/                             # 数据模型
│   ├── McpRequest.java                # MCP 请求模型
│   ├── McpResponse.java               # MCP 响应模型
│   └── ToolDefinition.java            # 工具定义模型
├── security/                          # 安全组件
│   └── ApiKeyAuthenticationFilter.java # API Key 认证过滤器
└── service/                           # 业务服务层
    ├── BusinessService.java           # 业务服务
    └── McpToolService.java            # MCP 工具服务

src/test/java/com/wanshifu/mcp/
├── controller/                        # 控制器测试
│   └── McpControllerTest.java
└── service/                           # 服务测试
    └── BusinessServiceTest.java

src/main/resources/
├── application.yml                    # 主配置文件
├── application-dev.yml                # 开发环境配置
├── application-test.yml               # 测试环境配置
├── application-prod.yml               # 生产环境配置
└── META-INF/
    └── app.properties                 # Apollo 配置
```

## 核心概念

### 1. MCP 工具注解

`@McpTool` 注解用于标记可以被 MCP 协议调用的方法：

```java
@McpTool(
    name = "query_user",                    // 工具名称（必需）
    description = "查询用户信息",            // 工具描述（必需）
    inputSchema = """                       // 输入参数 JSON Schema（必需）
        {
          "type": "object",
          "properties": {
            "userId": {
              "type": "string",
              "description": "用户ID"
            }
          },
          "required": ["userId"]
        }
        """
)
public Map<String, Object> queryUser(Map<String, Object> arguments) {
    String userId = (String) arguments.get("userId");
    // 业务逻辑实现
    return Map.of("success", true, "data", userData);
}
```

### 2. 工具注册机制

工具注册器 `ToolRegistry` 在应用启动时自动扫描所有带有 `@McpTool` 注解的方法：

```java
@Component
public class ToolRegistry implements ApplicationContextAware {
    
    @PostConstruct
    public void initialize() {
        scanAndRegisterTools();
    }
    
    private void scanAndRegisterTools() {
        // 扫描所有 Spring Bean 中的 @McpTool 方法
        // 注册到工具映射表中
    }
}
```

### 3. 请求处理流程

```
Client Request → Security Filter → MCP Controller → Tool Registry → Business Service → Response
```

## 开发最佳实践

### 1. 工具开发规范

#### 命名规范

```java
// ✅ 好的命名
@McpTool(name = "query_user", description = "查询用户信息")
@McpTool(name = "create_order", description = "创建订单")
@McpTool(name = "update_order_status", description = "更新订单状态")

// ❌ 不好的命名
@McpTool(name = "getUserInfo", description = "get user")
@McpTool(name = "order", description = "order operation")
```

#### 参数验证

```java
@McpTool(name = "create_user", description = "创建用户")
public Map<String, Object> createUser(Map<String, Object> arguments) {
    // 参数验证
    String username = validateString(arguments, "username", true);
    String email = validateEmail(arguments, "email", true);
    String phone = validateString(arguments, "phone", false);
    
    // 业务逻辑
    User user = userService.createUser(username, email, phone);
    
    // 返回结果
    return Map.of(
        "success", true,
        "userId", user.getId(),
        "message", "用户创建成功"
    );
}

private String validateString(Map<String, Object> args, String key, boolean required) {
    Object value = args.get(key);
    if (value == null) {
        if (required) {
            throw new IllegalArgumentException(key + " is required");
        }
        return null;
    }
    if (!(value instanceof String)) {
        throw new IllegalArgumentException(key + " must be a string");
    }
    return (String) value;
}
```

#### 错误处理

```java
@McpTool(name = "query_user", description = "查询用户信息")
public Map<String, Object> queryUser(Map<String, Object> arguments) {
    try {
        String userId = validateString(arguments, "userId", true);
        User user = userService.findById(userId);
        
        if (user == null) {
            return Map.of(
                "success", false,
                "error", "USER_NOT_FOUND",
                "message", "用户不存在"
            );
        }
        
        return Map.of(
            "success", true,
            "data", user.toMap()
        );
        
    } catch (IllegalArgumentException e) {
        return Map.of(
            "success", false,
            "error", "INVALID_PARAMETER",
            "message", e.getMessage()
        );
    } catch (Exception e) {
        logger.error("查询用户失败", e);
        return Map.of(
            "success", false,
            "error", "INTERNAL_ERROR",
            "message", "系统内部错误"
        );
    }
}
```

### 2. 测试开发

#### 单元测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "mcp.security.api-keys[0]=test-api-key-123456",
    "apollo.bootstrap.enabled=false"
})
class BusinessServiceTest {
    
    @Autowired
    private BusinessService businessService;
    
    @Test
    void testQueryUser() {
        // Given
        Map<String, Object> arguments = Map.of("userId", "user001");
        
        // When
        Map<String, Object> result = businessService.queryUser(arguments);
        
        // Then
        assertThat(result).containsEntry("success", true);
        assertThat(result).containsKey("data");
    }
    
    @Test
    void testQueryUserNotFound() {
        // Given
        Map<String, Object> arguments = Map.of("userId", "nonexistent");
        
        // When
        Map<String, Object> result = businessService.queryUser(arguments);
        
        // Then
        assertThat(result).containsEntry("success", false);
        assertThat(result).containsEntry("error", "USER_NOT_FOUND");
    }
}
```

#### 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class McpControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @LocalServerPort
    private int port;
    
    @Test
    void testMcpInitialize() {
        // Given
        String url = "http://localhost:" + port + "/mcp/initialize";
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-Key", "test-api-key-123456");
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, Object> request = Map.of(
            "jsonrpc", "2.0",
            "method", "initialize",
            "id", "1"
        );
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
        
        // When
        ResponseEntity<Map> response = restTemplate.exchange(
            url, HttpMethod.POST, entity, Map.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).containsKey("result");
    }
}
```

### 3. 调试技巧

#### 日志调试

```java
@Service
@Slf4j
public class BusinessService {
    
    @McpTool(name = "query_user", description = "查询用户信息")
    public Map<String, Object> queryUser(Map<String, Object> arguments) {
        log.debug("查询用户请求: {}", arguments);
        
        String userId = (String) arguments.get("userId");
        log.info("查询用户ID: {}", userId);
        
        try {
            User user = userService.findById(userId);
            log.debug("查询结果: {}", user);
            
            return Map.of("success", true, "data", user);
        } catch (Exception e) {
            log.error("查询用户失败: userId={}", userId, e);
            throw e;
        }
    }
}
```

#### 性能监控

```java
@Component
@Aspect
@Slf4j
public class PerformanceAspect {
    
    @Around("@annotation(mcpTool)")
    public Object measureExecutionTime(ProceedingJoinPoint joinPoint, McpTool mcpTool) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("工具 {} 执行时间: {}ms", mcpTool.name(), executionTime);
            
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("工具 {} 执行失败，耗时: {}ms", mcpTool.name(), executionTime, e);
            throw e;
        }
    }
}
```

## 扩展开发

### 1. 添加新的业务工具

#### 步骤 1: 定义工具方法

```java
@Service
public class OrderService {
    
    @McpTool(
        name = "create_order",
        description = "创建新订单",
        inputSchema = """
            {
              "type": "object",
              "properties": {
                "userId": {"type": "string", "description": "用户ID"},
                "serviceType": {"type": "string", "description": "服务类型"},
                "description": {"type": "string", "description": "订单描述"},
                "amount": {"type": "number", "description": "订单金额"}
              },
              "required": ["userId", "serviceType", "description", "amount"]
            }
            """
    )
    public Map<String, Object> createOrder(Map<String, Object> arguments) {
        // 实现订单创建逻辑
        return Map.of("success", true, "orderId", "ORDER_" + System.currentTimeMillis());
    }
}
```

#### 步骤 2: 编写测试

```java
@Test
void testCreateOrder() {
    Map<String, Object> arguments = Map.of(
        "userId", "user001",
        "serviceType", "REPAIR",
        "description", "空调维修",
        "amount", 200.0
    );
    
    Map<String, Object> result = orderService.createOrder(arguments);
    
    assertThat(result).containsEntry("success", true);
    assertThat(result).containsKey("orderId");
}
```

#### 步骤 3: 验证工具注册

```bash
# 启动应用后查看工具列表
curl -X POST http://localhost:8080/mcp/tools/list \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{"jsonrpc":"2.0","method":"tools/list","id":"1"}'
```

### 2. 集成外部系统

#### 数据库集成

```java
@Entity
@Table(name = "users")
public class User {
    @Id
    private String id;
    private String username;
    private String email;
    // getters and setters
}

@Repository
public interface UserRepository extends JpaRepository<User, String> {
    Optional<User> findByUsername(String username);
}

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @McpTool(name = "query_user_from_db", description = "从数据库查询用户")
    public Map<String, Object> queryUserFromDb(Map<String, Object> arguments) {
        String userId = (String) arguments.get("userId");
        Optional<User> user = userRepository.findById(userId);
        
        if (user.isPresent()) {
            return Map.of("success", true, "data", user.get());
        } else {
            return Map.of("success", false, "message", "用户不存在");
        }
    }
}
```

#### 微服务调用

```java
@Service
public class ExternalApiService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${external.api.base-url}")
    private String baseUrl;
    
    @McpTool(name = "get_external_data", description = "获取外部系统数据")
    public Map<String, Object> getExternalData(Map<String, Object> arguments) {
        String dataId = (String) arguments.get("dataId");
        
        try {
            String url = baseUrl + "/api/data/" + dataId;
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            return Map.of(
                "success", true,
                "data", response.getBody()
            );
        } catch (Exception e) {
            return Map.of(
                "success", false,
                "error", "EXTERNAL_API_ERROR",
                "message", e.getMessage()
            );
        }
    }
}
```

### 3. 自定义认证

```java
@Component
public class CustomAuthenticationProvider {
    
    public boolean validateCustomToken(String token) {
        // 实现自定义 token 验证逻辑
        return token != null && token.startsWith("CUSTOM_");
    }
}

@Component
public class CustomAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private CustomAuthenticationProvider authProvider;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String customToken = request.getHeader("X-Custom-Token");
        
        if (customToken != null && authProvider.validateCustomToken(customToken)) {
            // 设置认证信息
            SecurityContextHolder.getContext().setAuthentication(
                new UsernamePasswordAuthenticationToken("custom-user", null, 
                    Arrays.asList(new SimpleGrantedAuthority("ROLE_API_USER")))
            );
        }
        
        filterChain.doFilter(request, response);
    }
}
```

## 代码质量

### 1. 代码规范

使用 Checkstyle 和 SpotBugs 进行代码质量检查：

```xml
<!-- pom.xml -->
<plugin>
    <groupId>com.github.spotbugs</groupId>
    <artifactId>spotbugs-maven-plugin</artifactId>
    <version>4.7.3.0</version>
</plugin>

<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-checkstyle-plugin</artifactId>
    <version>3.3.0</version>
</plugin>
```

### 2. 测试覆盖率

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### 3. 持续集成

```yaml
# .github/workflows/ci.yml
name: CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
    
    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: Run tests
      run: mvn clean test
    
    - name: Generate test report
      run: mvn jacoco:report
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
```

## 常见问题

### 1. 工具未注册

**问题**: 添加了 `@McpTool` 注解但工具未出现在列表中

**解决方案**:
- 确保类被 Spring 管理（添加 `@Service` 或 `@Component`）
- 检查包扫描路径是否正确
- 查看启动日志中的工具注册信息

### 2. 参数解析错误

**问题**: 工具调用时参数类型不匹配

**解决方案**:
- 使用类型安全的参数验证方法
- 在 JSON Schema 中明确定义参数类型
- 添加详细的错误处理

### 3. 性能问题

**问题**: 工具执行时间过长

**解决方案**:
- 添加性能监控切面
- 使用异步处理长时间操作
- 实现结果缓存机制

```java
@Async
@McpTool(name = "async_operation", description = "异步操作")
public CompletableFuture<Map<String, Object>> asyncOperation(Map<String, Object> arguments) {
    return CompletableFuture.supplyAsync(() -> {
        // 长时间操作
        return Map.of("success", true, "result", "completed");
    });
}
```

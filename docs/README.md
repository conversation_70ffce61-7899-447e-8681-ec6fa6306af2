# 万师傅 MCP 服务文档中心

欢迎来到万师傅 MCP 服务文档中心！这里提供了完整的技术文档，帮助您快速了解、部署和使用万师傅 MCP 服务。

## 📚 文档导航

### 🚀 快速开始

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [README.md](../README.md) | 项目概览和快速开始指南 | 所有用户 |
| [API 参考](API.md) | 完整的 API 接口文档 | 开发者、集成商 |

### 🏗️ 架构与设计

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [架构设计](ARCHITECTURE.md) | 系统架构和设计原理 | 架构师、高级开发者 |
| [扩展指南](EXTENSIBILITY.md) | 如何扩展和定制服务 | 开发者 |

### ⚙️ 配置与部署

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [配置指南](CONFIGURATION.md) | 详细配置说明和最佳实践 | 运维工程师、开发者 |
| [部署指南](DEPLOYMENT.md) | 生产环境部署最佳实践 | 运维工程师、DevOps |
| [升级指南](UPGRADE_GUIDE.md) | 版本升级步骤和注意事项 | 运维工程师 |

### 🔒 安全与开发

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [安全指南](SECURITY.md) | 安全配置和防护措施 | 安全工程师、运维 |
| [开发指南](DEVELOPMENT.md) | 开发环境搭建和扩展开发 | 开发者 |
| [故障排查](TROUBLESHOOTING.md) | 常见问题诊断和解决方案 | 运维工程师、开发者 |

## 🎯 按角色分类

### 👨‍💻 开发者

如果您是开发者，建议按以下顺序阅读：

1. **入门**: [README.md](../README.md) → [API 参考](API.md)
2. **开发**: [开发指南](DEVELOPMENT.md) → [架构设计](ARCHITECTURE.md)
3. **扩展**: [扩展指南](EXTENSIBILITY.md) → [配置指南](CONFIGURATION.md)

### 🔧 运维工程师

如果您是运维工程师，建议按以下顺序阅读：

1. **部署**: [部署指南](DEPLOYMENT.md) → [配置指南](CONFIGURATION.md)
2. **安全**: [安全指南](SECURITY.md) → [架构设计](ARCHITECTURE.md)
3. **维护**: [升级指南](UPGRADE_GUIDE.md) → [API 参考](API.md)

### 🏢 架构师

如果您是架构师，建议按以下顺序阅读：

1. **架构**: [架构设计](ARCHITECTURE.md) → [安全指南](SECURITY.md)
2. **扩展**: [扩展指南](EXTENSIBILITY.md) → [开发指南](DEVELOPMENT.md)
3. **部署**: [部署指南](DEPLOYMENT.md) → [配置指南](CONFIGURATION.md)

### 🔌 集成商

如果您是第三方集成商，建议按以下顺序阅读：

1. **接口**: [API 参考](API.md) → [README.md](../README.md)
2. **安全**: [安全指南](SECURITY.md) → [配置指南](CONFIGURATION.md)
3. **扩展**: [扩展指南](EXTENSIBILITY.md) → [开发指南](DEVELOPMENT.md)

## 📖 文档特色

### ✨ 内容特点

- **📋 完整性**: 覆盖从入门到高级的所有主题
- **🎯 实用性**: 提供大量实际示例和最佳实践
- **🔄 时效性**: 与代码同步更新，确保准确性
- **👥 易读性**: 结构清晰，适合不同技术背景的读者

### 🛠️ 技术亮点

- **现代化架构**: 基于 Java 21 + Spring Boot 3.3.5
- **标准协议**: 完全符合 MCP 2025-06-18 规范
- **企业级安全**: 多层安全防护机制
- **云原生**: 支持 Docker、Kubernetes 部署
- **配置中心**: 集成 Apollo 动态配置管理

## 🔍 快速查找

### 常见问题快速定位

| 问题类型 | 相关文档 | 章节 |
|----------|----------|------|
| **如何快速启动服务？** | [README.md](../README.md) | 快速开始 |
| **API 如何调用？** | [API 参考](API.md) | 核心 API |
| **如何添加新工具？** | [开发指南](DEVELOPMENT.md) | 扩展开发 |
| **生产环境如何部署？** | [部署指南](DEPLOYMENT.md) | 部署方式 |
| **如何配置安全认证？** | [安全指南](SECURITY.md) | 认证机制 |
| **配置文件怎么写？** | [配置指南](CONFIGURATION.md) | 核心配置 |
| **系统架构是什么？** | [架构设计](ARCHITECTURE.md) | 系统架构概览 |
| **如何升级版本？** | [升级指南](UPGRADE_GUIDE.md) | 升级步骤 |

### 技术关键词索引

| 关键词 | 相关文档 | 说明 |
|--------|----------|------|
| **MCP 协议** | [API 参考](API.md) | Model Context Protocol 实现 |
| **Spring Boot** | [架构设计](ARCHITECTURE.md) | 应用框架 |
| **Java 21** | [开发指南](DEVELOPMENT.md) | 运行时环境 |
| **Apollo** | [配置指南](CONFIGURATION.md) | 配置中心 |
| **Docker** | [部署指南](DEPLOYMENT.md) | 容器化部署 |
| **Kubernetes** | [部署指南](DEPLOYMENT.md) | 容器编排 |
| **API Key** | [安全指南](SECURITY.md) | 认证方式 |
| **@McpTool** | [开发指南](DEVELOPMENT.md) | 工具注解 |

## 📝 文档贡献

### 如何贡献

我们欢迎社区贡献文档内容！如果您发现文档中的错误或希望添加新内容，请：

1. **报告问题**: 在 [GitHub Issues](https://github.com/wanshifu/mcp-service-demo/issues) 中报告文档问题
2. **提交改进**: 通过 Pull Request 提交文档改进
3. **建议新内容**: 在 [GitHub Discussions](https://github.com/wanshifu/mcp-service-demo/discussions) 中建议新的文档内容

### 文档规范

- **格式**: 使用 Markdown 格式
- **结构**: 保持清晰的层次结构
- **示例**: 提供实际可运行的代码示例
- **更新**: 与代码变更同步更新

## 🆘 获取帮助

### 技术支持

- **GitHub Issues**: [报告 Bug 和功能请求](https://github.com/wanshifu/mcp-service-demo/issues)
- **GitHub Discussions**: [技术讨论和问答](https://github.com/wanshifu/mcp-service-demo/discussions)
- **邮件支持**: <EMAIL>

### 社区资源

- **官方网站**: https://www.wanshifu.com
- **开发者社区**: 加入万师傅TC技术交流群

## 📊 文档统计

| 指标 | 数值 | 说明 |
|------|------|------|
| **文档数量** | 8 个 | 核心技术文档 |
| **总字数** | 50,000+ | 详细技术内容 |
| **代码示例** | 100+ | 实际可运行示例 |
| **更新频率** | 每月 | 与版本发布同步 |

---

**万师傅技术团队** © 2024 | 让文档成为最好的老师 📚

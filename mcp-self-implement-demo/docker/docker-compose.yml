version: '3.8'

services:
  mcp-service:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: wanshifu-mcp-service
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MCP_API_KEYS=${MCP_API_KEYS:-demo-api-key-123456,wanshifu-api-key-789012}
      - JAVA_OPTS=-Xmx512m -Xms256m
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - mcp-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: wanshifu-mcp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - mcp-service
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  mcp-logs:
    driver: local

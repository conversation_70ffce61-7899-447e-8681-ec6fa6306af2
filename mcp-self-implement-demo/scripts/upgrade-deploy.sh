#!/bin/bash

# 万师傅MCP服务升级部署脚本
# 从JDK 8 + Spring Boot 2.7.18 升级到 JDK 21 + Spring Boot 3.3.5

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到 macOS 系统"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_info "检测到 Linux 系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查是否有sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_warning "需要sudo权限来安装软件"
    fi
}

# 安装JDK 21
install_jdk21() {
    log_info "安装JDK 21..."
    
    if command -v java >/dev/null 2>&1; then
        JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
        if [[ $JAVA_VERSION == 21* ]]; then
            log_success "JDK 21 已安装"
            return 0
        fi
    fi
    
    case $OS in
        "macos")
            if command -v brew >/dev/null 2>&1; then
                brew install openjdk@21
                echo 'export PATH="/opt/homebrew/opt/openjdk@21/bin:$PATH"' >> ~/.zshrc
                export PATH="/opt/homebrew/opt/openjdk@21/bin:$PATH"
            else
                log_error "请先安装 Homebrew: https://brew.sh/"
                exit 1
            fi
            ;;
        "linux")
            if command -v apt-get >/dev/null 2>&1; then
                sudo apt-get update
                sudo apt-get install -y openjdk-21-jdk
            elif command -v yum >/dev/null 2>&1; then
                sudo yum install -y java-21-openjdk-devel
            else
                log_error "不支持的Linux发行版"
                exit 1
            fi
            ;;
    esac
    
    log_success "JDK 21 安装完成"
}

# 安装Maven 3.9+
install_maven() {
    log_info "安装Maven 3.9+..."
    
    if command -v mvn >/dev/null 2>&1; then
        MAVEN_VERSION=$(mvn -version | head -n1 | cut -d' ' -f3)
        if [[ $(echo "$MAVEN_VERSION 3.9.0" | tr " " "\n" | sort -V | head -n1) == "3.9.0" ]]; then
            log_success "Maven 3.9+ 已安装"
            return 0
        fi
    fi
    
    case $OS in
        "macos")
            if command -v brew >/dev/null 2>&1; then
                brew install maven
            else
                log_error "请先安装 Homebrew"
                exit 1
            fi
            ;;
        "linux")
            # 下载并安装Maven
            MAVEN_VERSION="3.9.9"
            cd /tmp
            wget https://archive.apache.org/dist/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz
            sudo tar -xzf apache-maven-${MAVEN_VERSION}-bin.tar.gz -C /opt/
            sudo ln -sf /opt/apache-maven-${MAVEN_VERSION}/bin/mvn /usr/local/bin/mvn
            ;;
    esac
    
    log_success "Maven 安装完成"
}

# 备份当前版本
backup_current_version() {
    log_info "备份当前版本..."
    
    BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份源代码
    cp -r src "$BACKUP_DIR/"
    cp pom.xml "$BACKUP_DIR/"
    cp -r docker "$BACKUP_DIR/" 2>/dev/null || true
    
    # 如果有运行中的服务，备份配置
    if docker ps | grep -q mcp-service; then
        docker logs mcp-service > "$BACKUP_DIR/service.log" 2>&1 || true
    fi
    
    log_success "备份完成: $BACKUP_DIR"
    echo "$BACKUP_DIR" > .backup_location
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    # 清理并编译
    mvn clean compile -DskipTests
    
    if [ $? -eq 0 ]; then
        log_success "编译成功"
    else
        log_error "编译失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    mvn test
    
    if [ $? -eq 0 ]; then
        log_success "测试通过"
    else
        log_warning "测试失败，但继续部署"
    fi
}

# 构建Docker镜像
build_docker_image() {
    log_info "构建Docker镜像..."
    
    # 打包应用
    mvn package -DskipTests
    
    # 构建Docker镜像
    docker build -f docker/Dockerfile -t mcp-service-demo:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 停止旧服务
stop_old_service() {
    log_info "停止旧服务..."
    
    if docker ps | grep -q mcp-service; then
        docker stop mcp-service
        docker rm mcp-service
        log_success "旧服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 启动新服务
start_new_service() {
    log_info "启动新服务..."
    
    # 使用docker-compose启动
    if [ -f "docker/docker-compose.yml" ]; then
        cd docker
        docker-compose up -d
        cd ..
    else
        # 直接使用docker run
        docker run -d \
            --name mcp-service \
            -p 8080:8080 \
            -e SPRING_PROFILES_ACTIVE=prod \
            -e APOLLO_META_SERVER=${APOLLO_META_SERVER:-http://localhost:8080} \
            -e APOLLO_ENV=${APOLLO_ENV:-PROD} \
            mcp-service-demo:latest
    fi
    
    log_success "新服务已启动"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
            log_success "服务健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "服务健康检查失败"
    return 1
}

# 功能测试
functional_test() {
    log_info "执行功能测试..."
    
    # 测试MCP初始化
    local response=$(curl -s -X POST http://localhost:8080/mcp/initialize \
        -H "Content-Type: application/json" \
        -H "X-API-Key: demo-api-key-123456" \
        -d '{
            "jsonrpc": "2.0",
            "method": "initialize",
            "params": {
                "protocolVersion": "2025-06-18",
                "capabilities": {
                    "roots": {"listChanged": true},
                    "sampling": {},
                    "elicitation": {}
                },
                "clientInfo": {
                    "name": "TestClient",
                    "version": "1.0.0"
                }
            },
            "id": "1"
        }')
    
    if echo "$response" | grep -q "protocolVersion"; then
        log_success "MCP功能测试通过"
        return 0
    else
        log_error "MCP功能测试失败"
        log_error "响应: $response"
        return 1
    fi
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    if [ -f ".backup_location" ]; then
        BACKUP_DIR=$(cat .backup_location)
        if [ -d "$BACKUP_DIR" ]; then
            # 停止当前服务
            stop_old_service
            
            # 恢复文件
            cp -r "$BACKUP_DIR/src" .
            cp "$BACKUP_DIR/pom.xml" .
            cp -r "$BACKUP_DIR/docker" . 2>/dev/null || true
            
            # 重新构建和启动
            build_docker_image
            start_new_service
            
            log_success "回滚完成"
        else
            log_error "备份目录不存在: $BACKUP_DIR"
        fi
    else
        log_error "找不到备份位置"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理Docker镜像
    docker image prune -f
    
    log_success "清理完成"
}

# 主函数
main() {
    log_info "开始万师傅MCP服务升级部署..."
    log_info "目标版本: JDK 21 + Spring Boot 3.3.5 + Apollo配置中心"
    
    # 设置错误处理
    trap 'log_error "升级失败，开始回滚..."; rollback; exit 1' ERR
    
    check_environment
    install_jdk21
    install_maven
    backup_current_version
    compile_project
    run_tests
    build_docker_image
    stop_old_service
    start_new_service
    
    if health_check && functional_test; then
        log_success "升级部署成功完成！"
        
        echo ""
        log_info "服务信息:"
        echo "  - 健康检查: http://localhost:8080/actuator/health"
        echo "  - MCP服务: http://localhost:8080/mcp/"
        echo "  - API Key: demo-api-key-123456"
        echo ""
        log_info "技术栈:"
        echo "  - JDK: $(java -version 2>&1 | head -n1)"
        echo "  - Spring Boot: 3.3.5"
        echo "  - Apollo: 2.3.0"
        
        cleanup
    else
        log_error "升级验证失败"
        rollback
        exit 1
    fi
}

# 执行主函数
main "$@"

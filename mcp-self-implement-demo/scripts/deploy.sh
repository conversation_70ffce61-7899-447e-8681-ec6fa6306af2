#!/bin/bash

# 万师傅 MCP 服务部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 示例: ./deploy.sh prod 1.0.0

set -e

# 默认参数
ENVIRONMENT=${1:-dev}
VERSION=${2:-latest}
SERVICE_NAME="wanshifu-mcp-service"
IMAGE_NAME="wanshifu/mcp-service"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 构建镜像
build_image() {
    log_info "开始构建 Docker 镜像..."
    
    cd "$(dirname "$0")/.."
    
    # 构建镜像
    docker build -f docker/Dockerfile -t ${IMAGE_NAME}:${VERSION} .
    docker tag ${IMAGE_NAME}:${VERSION} ${IMAGE_NAME}:latest
    
    log_info "镜像构建完成: ${IMAGE_NAME}:${VERSION}"
}

# 停止现有服务
stop_service() {
    log_info "停止现有服务..."
    
    if docker ps -q --filter "name=${SERVICE_NAME}" | grep -q .; then
        docker stop ${SERVICE_NAME}
        docker rm ${SERVICE_NAME}
        log_info "已停止现有服务"
    else
        log_info "没有运行中的服务"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    cd docker
    
    # 设置环境变量
    export SPRING_PROFILES_ACTIVE=${ENVIRONMENT}
    export IMAGE_TAG=${VERSION}
    
    # 启动服务
    docker-compose up -d
    
    log_info "服务启动完成"
}

# 健康检查
health_check() {
    log_info "等待服务启动..."
    sleep 30
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查 (${attempt}/${max_attempts})..."
        
        if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_info "服务健康检查通过"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_error "服务健康检查失败"
    return 1
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker ps --filter "name=${SERVICE_NAME}"
    
    log_info "服务日志:"
    docker logs --tail 20 ${SERVICE_NAME}
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    # 删除无标签镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q)
    fi
    
    log_info "清理完成"
}

# 主函数
main() {
    log_info "开始部署 MCP 服务..."
    log_info "环境: ${ENVIRONMENT}"
    log_info "版本: ${VERSION}"
    
    check_docker
    build_image
    stop_service
    start_service
    
    if health_check; then
        show_status
        cleanup
        log_info "部署成功完成!"
        
        echo ""
        log_info "服务访问地址:"
        echo "  - 健康检查: http://localhost:8080/actuator/health"
        echo "  - MCP 服务: http://localhost:8080/mcp/"
        echo ""
        log_info "API Key: demo-api-key-123456"
        
    else
        log_error "部署失败，请检查日志"
        docker logs ${SERVICE_NAME}
        exit 1
    fi
}

# 执行主函数
main "$@"

#!/bin/bash

# MCP 服务 API 测试脚本
# 使用方法: ./test-api.sh [服务地址]
# 示例: ./test-api.sh http://localhost:8080

set -e

# 默认参数
BASE_URL=${1:-http://localhost:8080}
API_KEY="demo-api-key-123456"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    local expected_status="${5:-200}"
    
    log_test "测试: $name"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X "$method" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "$data" \
        "$BASE_URL$endpoint")
    
    local body=$(echo "$response" | head -n -1)
    local status=$(echo "$response" | tail -n 1)
    
    if [ "$status" -eq "$expected_status" ]; then
        log_success "状态码: $status ✓"
        echo "响应: $body" | jq '.' 2>/dev/null || echo "响应: $body"
    else
        log_error "状态码: $status (期望: $expected_status) ✗"
        echo "响应: $body"
        return 1
    fi
    
    echo ""
}

# 主测试流程
main() {
    log_info "开始测试 MCP 服务 API"
    log_info "服务地址: $BASE_URL"
    log_info "API Key: $API_KEY"
    echo ""
    
    # 1. 健康检查
    test_api "健康检查" "GET" "/actuator/health" ""
    
    # 2. MCP 初始化（符合2025-06-18规范）
    test_api "MCP 初始化" "POST" "/mcp/initialize" '{
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {
                "roots": {"listChanged": true},
                "sampling": {},
                "elicitation": {}
            },
            "clientInfo": {
                "name": "TestClient",
                "version": "1.0.0"
            }
        },
        "id": "1"
    }'
    
    # 3. 获取工具列表
    test_api "获取工具列表" "POST" "/mcp/tools/list" '{
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": "2"
    }'
    
    # 4. 查询用户信息
    test_api "查询用户信息" "POST" "/mcp/tools/call" '{
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "query_user",
            "arguments": {
                "userId": "user001"
            }
        },
        "id": "3"
    }'
    
    # 5. 查询订单信息
    test_api "查询订单信息" "POST" "/mcp/tools/call" '{
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "query_order",
            "arguments": {
                "userId": "user001"
            }
        },
        "id": "4"
    }'
    
    # 6. 更新订单状态
    test_api "更新订单状态" "POST" "/mcp/tools/call" '{
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "update_order_status",
            "arguments": {
                "orderId": "order002",
                "status": "PAID",
                "reason": "API测试更新"
            }
        },
        "id": "5"
    }'
    
    # 7. 发送通知
    test_api "发送通知" "POST" "/mcp/tools/call" '{
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "send_notification",
            "arguments": {
                "userId": "user001",
                "type": "SMS",
                "title": "API测试通知",
                "content": "这是一条API测试通知消息"
            }
        },
        "id": "6"
    }'
    
    # 8. 测试无效API Key
    log_test "测试无效API Key"
    local invalid_response=$(curl -s -w "\n%{http_code}" \
        -X "POST" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: invalid-key" \
        -d '{"jsonrpc": "2.0", "method": "initialize", "params": {}, "id": "7"}' \
        "$BASE_URL/mcp/initialize")
    
    local invalid_status=$(echo "$invalid_response" | tail -n 1)
    if [ "$invalid_status" -eq "401" ]; then
        log_success "无效API Key测试通过 ✓"
    else
        log_error "无效API Key测试失败 ✗"
    fi
    echo ""
    
    # 9. 测试不存在的工具
    test_api "测试不存在的工具" "POST" "/mcp/tools/call" '{
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "non_existent_tool",
            "arguments": {}
        },
        "id": "8"
    }'
    
    log_info "所有测试完成!"
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_info "jq 未安装，JSON 响应将以原始格式显示"
    fi
}

# 执行测试
check_dependencies
main "$@"

package com.wanshifu.mcp.security;

import com.wanshifu.mcp.config.McpProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Enumeration;

/**
 * API Key认证过滤器
 * 
 * <AUTHOR> Team
 */
@Component
public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiKeyAuthenticationFilter.class);
    
    private static final String API_KEY_HEADER = "X-API-Key";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    @Autowired
    private McpProperties mcpProperties;

    // 不需要认证的端点列表
    private static final List<String> SKIP_AUTH_PATHS = Arrays.asList(
        "/actuator/health",           // 健康检查
        "/actuator/info",             // 应用信息
        "/actuator/metrics",          // 指标监控
        "/services/config",           // 服务配置
        "/services/health",           // 服务健康检查
        "/favicon.ico",               // 网站图标
        "/error"                      // 错误页面
    );
    
    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestURI = request.getRequestURI();

        // 跳过不需要认证的端点
        if (shouldSkipAuthentication(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 打印请求头信息
        logRequestHeaders(request);
        
        try {
            String apiKey = extractApiKey(request);
            
            if (StringUtils.hasText(apiKey) && isValidApiKey(apiKey)) {
                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(
                        "api-user",
                        null,
                        Arrays.asList(new SimpleGrantedAuthority("ROLE_API_USER"))
                    );

                SecurityContextHolder.getContext().setAuthentication(authentication);
                logger.debug("API Key authentication successful for request: {}", requestURI);

                // 继续处理请求
                filterChain.doFilter(request, response);
            } else {
                logger.warn("Invalid or missing API Key for request: {}", requestURI);

                // 返回401未授权状态，使用MCP标准错误格式
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("{\"error\":{\"code\":-32001,\"message\":\"Unauthorized: Invalid or missing API key\"}}");
                return;
            }

        } catch (Exception e) {
            logger.error("API Key authentication failed", e);

            // 返回500内部服务器错误
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write("{\"error\":\"Internal Server Error\",\"message\":\"Authentication failed\"}");
            return;
        }
    }
    
    /**
     * 打印请求头信息
     */
    private void logRequestHeaders(HttpServletRequest request) {
        logger.info("=== MCP Request Headers ===");
        logger.info("Request URL: {} {}", request.getMethod(), request.getRequestURL());
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            logger.info("Header: {} = {}", headerName, headerValue);
        }
        logger.info("==========================");
    }
    
    /**
     * 从请求中提取API Key
     */
    private String extractApiKey(HttpServletRequest request) {
        // 首先尝试从X-API-Key头部获取
        String apiKey = request.getHeader(API_KEY_HEADER);
        
        if (!StringUtils.hasText(apiKey)) {
            // 尝试从Authorization头部获取（Bearer token格式）
            String authHeader = request.getHeader(AUTHORIZATION_HEADER);
            if (StringUtils.hasText(authHeader) && authHeader.startsWith(BEARER_PREFIX)) {
                apiKey = authHeader.substring(BEARER_PREFIX.length());
            }
        }
        
        return apiKey;
    }
    
    /**
     * 验证API Key是否有效
     */
    private boolean isValidApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return false;
        }

        List<String> validKeys = mcpProperties.getSecurity().getApiKeys();
        if (validKeys == null || validKeys.isEmpty()) {
            return false;
        }

        return validKeys.contains(apiKey.trim());
    }

    /**
     * 判断是否应该跳过认证
     */
    private boolean shouldSkipAuthentication(String requestURI) {
        // 精确匹配
        if (SKIP_AUTH_PATHS.contains(requestURI)) {
            return true;
        }

        // 路径前缀匹配
        return requestURI.startsWith("/actuator/") ||
               requestURI.startsWith("/services/") ||
               requestURI.startsWith("/static/") ||
               requestURI.startsWith("/public/");
    }
}
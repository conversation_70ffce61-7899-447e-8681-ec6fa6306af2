package com.wanshifu.mcp.core;

import com.wanshifu.mcp.annotation.McpTool;
import com.wanshifu.mcp.annotation.McpToolParam;
import com.wanshifu.mcp.model.Tool;
import com.wanshifu.mcp.model.ToolResult;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP工具注册表 - 自动发现和注册工具
 * 
 * <AUTHOR> Team
 */
@Component
public class ToolRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolRegistry.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private final Map<String, ToolDefinition> tools = new ConcurrentHashMap<>();
    private final List<ToolRegistryListener> listeners = new ArrayList<>();
    
    @PostConstruct
    public void initialize() {
        scanAndRegisterTools();
    }
    
    /**
     * 扫描并注册所有MCP工具
     */
    private void scanAndRegisterTools() {
        logger.info("开始扫描MCP工具...");

        // 获取所有Spring Bean，但跳过控制器类以避免循环依赖
        String[] beanNames = applicationContext.getBeanDefinitionNames();

        for (String beanName : beanNames) {
            try {
                // 跳过控制器类以避免循环依赖
                if (beanName.contains("Controller")) {
                    continue;
                }

                Object bean = applicationContext.getBean(beanName);
                Class<?> beanClass = bean.getClass();

                // 扫描类中的所有方法
                for (Method method : beanClass.getDeclaredMethods()) {
                    McpTool toolAnnotation = method.getAnnotation(McpTool.class);
                    if (toolAnnotation != null) {
                        registerTool(bean, method, toolAnnotation);
                    }
                }
            } catch (Exception e) {
                logger.debug("跳过Bean: {} (可能存在循环依赖)", beanName);
            }
        }

        logger.info("MCP工具扫描完成，共注册 {} 个工具", tools.size());
        notifyListeners();
    }
    
    /**
     * 注册单个工具
     */
    private void registerTool(Object bean, Method method, McpTool toolAnnotation) {
        String toolName = toolAnnotation.name();
        
        try {
            // 生成工具定义
            Tool tool = generateToolDefinition(method, toolAnnotation);
            
            // 创建工具定义对象
            ToolDefinition toolDef = new ToolDefinition(tool, bean, method);
            
            tools.put(toolName, toolDef);
            logger.debug("注册工具: {} -> {}.{}", toolName, bean.getClass().getSimpleName(), method.getName());
            
        } catch (Exception e) {
            logger.error("注册工具失败: {}", toolName, e);
        }
    }
    
    /**
     * 生成工具定义
     */
    private Tool generateToolDefinition(Method method, McpTool toolAnnotation) {
        String name = toolAnnotation.name();
        String title = toolAnnotation.title().isEmpty() ? name : toolAnnotation.title();
        String description = toolAnnotation.description();
        
        // 生成输入Schema
        Map<String, Object> inputSchema = generateInputSchema(method);
        
        Tool tool = new Tool(name, description, inputSchema);
        tool.setTitle(title);
        
        return tool;
    }
    
    /**
     * 生成输入Schema
     */
    private Map<String, Object> generateInputSchema(Method method) {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        List<String> required = new ArrayList<>();
        
        Parameter[] parameters = method.getParameters();
        for (Parameter parameter : parameters) {
            McpToolParam paramAnnotation = parameter.getAnnotation(McpToolParam.class);
            if (paramAnnotation != null) {
                String paramName = paramAnnotation.name();
                Map<String, Object> paramSchema = generateParameterSchema(parameter, paramAnnotation);
                properties.put(paramName, paramSchema);
                
                if (paramAnnotation.required()) {
                    required.add(paramName);
                }
            }
        }
        
        schema.put("properties", properties);
        if (!required.isEmpty()) {
            schema.put("required", required);
        }
        
        return schema;
    }
    
    /**
     * 生成参数Schema
     */
    private Map<String, Object> generateParameterSchema(Parameter parameter, McpToolParam paramAnnotation) {
        Map<String, Object> paramSchema = new HashMap<>();
        
        // 类型推断
        String type = paramAnnotation.type();
        if (type.isEmpty()) {
            type = inferTypeFromClass(parameter.getType());
        }
        paramSchema.put("type", type);
        
        // 描述
        paramSchema.put("description", paramAnnotation.description());
        
        // 枚举值
        if (paramAnnotation.enumValues().length > 0) {
            paramSchema.put("enum", Arrays.asList(paramAnnotation.enumValues()));
        }
        
        // 示例值
        if (!paramAnnotation.example().isEmpty()) {
            paramSchema.put("example", paramAnnotation.example());
        }
        
        return paramSchema;
    }
    
    /**
     * 从Java类型推断JSON Schema类型
     */
    private String inferTypeFromClass(Class<?> clazz) {
        if (String.class.equals(clazz)) {
            return "string";
        } else if (Integer.class.equals(clazz) || int.class.equals(clazz) ||
                   Long.class.equals(clazz) || long.class.equals(clazz)) {
            return "integer";
        } else if (Double.class.equals(clazz) || double.class.equals(clazz) ||
                   Float.class.equals(clazz) || float.class.equals(clazz)) {
            return "number";
        } else if (Boolean.class.equals(clazz) || boolean.class.equals(clazz)) {
            return "boolean";
        } else if (List.class.isAssignableFrom(clazz) || clazz.isArray()) {
            return "array";
        } else {
            return "object";
        }
    }
    
    /**
     * 获取所有工具
     */
    public List<Tool> getAllTools() {
        return tools.values().stream()
                .map(ToolDefinition::getTool)
                .toList();
    }
    
    /**
     * 获取工具定义
     */
    public ToolDefinition getToolDefinition(String name) {
        return tools.get(name);
    }
    
    /**
     * 检查工具是否存在
     */
    public boolean hasToolName(String name) {
        return tools.containsKey(name);
    }
    
    /**
     * 添加监听器
     */
    public void addListener(ToolRegistryListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 通知监听器
     */
    private void notifyListeners() {
        for (ToolRegistryListener listener : listeners) {
            try {
                listener.onToolsChanged();
            } catch (Exception e) {
                logger.error("通知工具注册监听器失败", e);
            }
        }
    }
    
    /**
     * 工具定义内部类
     */
    public static class ToolDefinition {
        private final Tool tool;
        private final Object bean;
        private final Method method;
        
        public ToolDefinition(Tool tool, Object bean, Method method) {
            this.tool = tool;
            this.bean = bean;
            this.method = method;
        }
        
        public Tool getTool() {
            return tool;
        }
        
        public Object getBean() {
            return bean;
        }
        
        public Method getMethod() {
            return method;
        }
    }
    
    /**
     * 工具注册监听器接口
     */
    public interface ToolRegistryListener {
        void onToolsChanged();
    }
}

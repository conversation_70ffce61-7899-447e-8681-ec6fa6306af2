package com.wanshifu.mcp.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanshifu.mcp.annotation.McpToolParam;
import com.wanshifu.mcp.model.Content;
import com.wanshifu.mcp.model.ToolResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MCP工具执行器 - 负责调用工具方法
 * 
 * <AUTHOR> Team
 */
@Component
public class ToolExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolExecutor.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 执行工具调用
     */
    public ToolResult executeTool(ToolRegistry.ToolDefinition toolDef, Map<String, Object> arguments) {
        String toolName = toolDef.getTool().getName();
        long startTime = System.currentTimeMillis();

        try {
            logger.info("开始执行工具: {}, 输入参数: {}", toolName, arguments);

            Object bean = toolDef.getBean();
            Method method = toolDef.getMethod();
            
            // 准备方法参数
            Object[] methodArgs = prepareMethodArguments(method, arguments);
            logger.debug("工具 {} 方法参数准备完成: {}", toolName, methodArgs);

            // 调用方法
            Object result = method.invoke(bean, methodArgs);
            long executionTime = System.currentTimeMillis() - startTime;

            logger.info("工具 {} 执行成功, 耗时: {}ms, 原始结果: {}", toolName, executionTime, result);

            // 转换结果
            ToolResult toolResult = convertResult(result);
            logger.info("工具 {} 结果转换完成: {}", toolName, toolResult);

            return toolResult;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("工具 {} 执行失败, 耗时: {}ms, 参数: {}", toolName, executionTime, arguments, e);

            ToolResult errorResult = createErrorResult("工具执行失败: " + e.getMessage());
            logger.error("工具 {} 错误结果: {}", toolName, errorResult);

            return errorResult;
        }
    }
    
    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Map<String, Object> arguments) {
        Parameter[] parameters = method.getParameters();
        Object[] methodArgs = new Object[parameters.length];
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            McpToolParam paramAnnotation = parameter.getAnnotation(McpToolParam.class);
            
            if (paramAnnotation != null) {
                String paramName = paramAnnotation.name();
                Object value = arguments.get(paramName);
                
                // 类型转换
                methodArgs[i] = convertParameterValue(value, parameter.getType());
            } else {
                // 如果没有注解，尝试按参数名获取
                String paramName = parameter.getName();
                Object value = arguments.get(paramName);
                methodArgs[i] = convertParameterValue(value, parameter.getType());
            }
        }
        
        return methodArgs;
    }
    
    /**
     * 转换参数值
     */
    private Object convertParameterValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        try {
            // 使用ObjectMapper进行类型转换
            return objectMapper.convertValue(value, targetType);
        } catch (Exception e) {
            logger.warn("参数类型转换失败: {} -> {}", value.getClass(), targetType, e);
            return value;
        }
    }
    
    /**
     * 转换执行结果
     */
    private ToolResult convertResult(Object result) {
        logger.debug("开始转换结果, 原始结果类型: {}, 内容: {}",
                    result != null ? result.getClass().getSimpleName() : "null", result);

        if (result == null) {
            logger.debug("结果为null, 返回默认成功消息");
            return createSuccessResult("操作完成");
        }

        if (result instanceof ToolResult) {
            logger.debug("结果已经是ToolResult类型, 直接返回");
            return (ToolResult) result;
        }

        if (result instanceof String) {
            logger.debug("结果为字符串: {}", result);
            return createSuccessResult((String) result);
        }
        
        if (result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> resultMap = (Map<String, Object>) result;
            logger.debug("结果为Map类型: {}", resultMap);

            // 检查是否是业务结果格式
            if (resultMap.containsKey("success")) {
                Boolean success = (Boolean) resultMap.get("success");
                String message = (String) resultMap.get("message");
                Object data = resultMap.get("data");

                logger.debug("检测到业务结果格式: success={}, message={}, data={}", success, message, data);

                if (Boolean.TRUE.equals(success)) {
                    logger.debug("业务操作成功, 创建成功结果");
                    return createSuccessResultWithData(message, data);
                } else {
                    logger.debug("业务操作失败, 创建错误结果");
                    return createErrorResult(message != null ? message : "操作失败");
                }
            }
            logger.debug("Map不包含success字段, 将作为普通数据处理");
        }
        
        // 默认转换为JSON字符串
        logger.debug("使用默认转换方式, 将结果序列化为JSON");
        try {
            String jsonResult = objectMapper.writeValueAsString(result);
            logger.debug("结果序列化成功: {}", jsonResult);
            return createSuccessResult(jsonResult);
        } catch (Exception e) {
            logger.error("结果序列化失败, 原始结果: {}", result, e);
            return createErrorResult("结果序列化失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建成功结果
     */
    private ToolResult createSuccessResult(String text) {
        List<Content> content = List.of(new Content.TextContent(text));
        ToolResult result = new ToolResult(content, false);
        logger.debug("创建成功结果: text={}, result={}", text, result);
        return result;
    }
    
    /**
     * 创建带数据的成功结果
     */
    private ToolResult createSuccessResultWithData(String message, Object data) {
        List<Content> contentList = new ArrayList<>();
        
        if (message != null) {
            contentList.add(new Content.TextContent("✅ " + message));
        }
        
        if (data != null) {
            try {
                String dataJson = objectMapper.writeValueAsString(data);
                contentList.add(new Content.TextContent("结果: " + dataJson));
                logger.debug("数据序列化成功: {}", dataJson);
            } catch (Exception e) {
                String dataStr = data.toString();
                contentList.add(new Content.TextContent("结果: " + dataStr));
                logger.debug("数据序列化失败，使用toString: {}", dataStr, e);
            }
        }

        ToolResult result = new ToolResult(contentList, false);
        logger.debug("创建带数据的成功结果: message={}, data={}, result={}", message, data, result);
        return result;
    }
    
    /**
     * 创建错误结果
     */
    private ToolResult createErrorResult(String errorMessage) {
        List<Content> content = List.of(new Content.TextContent("❌ " + errorMessage));
        ToolResult result = new ToolResult(content, true);
        logger.debug("创建错误结果: errorMessage={}, result={}", errorMessage, result);
        return result;
    }
}

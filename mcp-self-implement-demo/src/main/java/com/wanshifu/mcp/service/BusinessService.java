package com.wanshifu.mcp.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 业务服务 - 模拟内部业务系统的能力
 * 
 * <AUTHOR> Team
 */
@Service
public class BusinessService {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessService.class);
    
    // 模拟用户数据
    private final Map<String, Map<String, Object>> users = new HashMap<>();
    // 模拟订单数据
    private final Map<String, Map<String, Object>> orders = new HashMap<>();
    
    public BusinessService() {
        initializeMockData();
    }
    
    /**
     * 初始化模拟数据
     */
    private void initializeMockData() {
        // 模拟用户数据
        users.put("user001", Map.of(
            "userId", "user001",
            "name", "张三",
            "phone", "***********",
            "email", "<EMAIL>",
            "status", "ACTIVE",
            "createTime", "2024-01-15 10:30:00"
        ));
        
        users.put("user002", Map.of(
            "userId", "user002",
            "name", "李四",
            "phone", "***********",
            "email", "<EMAIL>",
            "status", "ACTIVE",
            "createTime", "2024-02-20 14:20:00"
        ));
        
        // 模拟订单数据
        orders.put("order001", Map.of(
            "orderId", "order001",
            "userId", "user001",
            "productName", "万师傅安装服务",
            "amount", 299.00,
            "status", "DELIVERED",
            "createTime", "2024-07-15 09:15:00",
            "updateTime", "2024-07-16 16:30:00"
        ));
        
        orders.put("order002", Map.of(
            "orderId", "order002",
            "userId", "user001",
            "productName", "家具维修服务",
            "amount", 150.00,
            "status", "PENDING",
            "createTime", "2024-07-20 11:45:00",
            "updateTime", "2024-07-20 11:45:00"
        ));
        
        orders.put("order003", Map.of(
            "orderId", "order003",
            "userId", "user002",
            "productName", "家电清洗服务",
            "amount", 80.00,
            "status", "PAID",
            "createTime", "2024-07-18 15:20:00",
            "updateTime", "2024-07-18 16:00:00"
        ));
    }
    
    /**
     * 查询用户信息
     */
    public Map<String, Object> queryUser(Map<String, Object> params) {
        logger.info("查询用户信息: {}", params);
        
        String userId = (String) params.get("userId");
        String phone = (String) params.get("phone");
        String email = (String) params.get("email");
        
        Map<String, Object> user = null;
        
        if (userId != null) {
            user = users.get(userId);
        } else if (phone != null) {
            user = users.values().stream()
                    .filter(u -> phone.equals(u.get("phone")))
                    .findFirst()
                    .orElse(null);
        } else if (email != null) {
            user = users.values().stream()
                    .filter(u -> email.equals(u.get("email")))
                    .findFirst()
                    .orElse(null);
        }
        
        if (user != null) {
            return Map.of(
                "success", true,
                "data", user,
                "message", "用户信息查询成功"
            );
        } else {
            return Map.of(
                "success", false,
                "data", null,
                "message", "未找到匹配的用户信息"
            );
        }
    }
    
    /**
     * 查询订单信息
     */
    public Map<String, Object> queryOrder(Map<String, Object> params) {
        logger.info("查询订单信息: {}", params);
        
        String orderId = (String) params.get("orderId");
        String userId = (String) params.get("userId");
        String status = (String) params.get("status");
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        if (orderId != null) {
            Map<String, Object> order = orders.get(orderId);
            if (order != null) {
                results.add(order);
            }
        } else {
            // 根据其他条件筛选
            for (Map<String, Object> order : orders.values()) {
                boolean match = true;
                
                if (userId != null && !userId.equals(order.get("userId"))) {
                    match = false;
                }
                
                if (status != null && !status.equals(order.get("status"))) {
                    match = false;
                }
                
                if (match) {
                    results.add(order);
                }
            }
        }
        
        return Map.of(
            "success", true,
            "data", results,
            "total", results.size(),
            "message", "订单信息查询成功"
        );
    }
    
    /**
     * 更新订单状态
     */
    public Map<String, Object> updateOrderStatus(Map<String, Object> params) {
        logger.info("更新订单状态: {}", params);
        
        String orderId = (String) params.get("orderId");
        String newStatus = (String) params.get("status");
        String reason = (String) params.get("reason");
        
        Map<String, Object> order = orders.get(orderId);
        if (order == null) {
            return Map.of(
                "success", false,
                "message", "订单不存在: " + orderId
            );
        }
        
        // 创建新的订单对象（模拟更新）
        Map<String, Object> updatedOrder = new HashMap<>(order);
        updatedOrder.put("status", newStatus);
        updatedOrder.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (reason != null) {
            updatedOrder.put("statusChangeReason", reason);
        }
        
        orders.put(orderId, updatedOrder);
        
        return Map.of(
            "success", true,
            "data", updatedOrder,
            "message", "订单状态更新成功"
        );
    }
    
    /**
     * 发送通知
     */
    public Map<String, Object> sendNotification(Map<String, Object> params) {
        logger.info("发送通知: {}", params);

        String userId = (String) params.get("userId");
        String type = (String) params.get("type");
        String title = (String) params.get("title");
        String content = (String) params.get("content");

        // 检查用户是否存在
        Map<String, Object> user = users.get(userId);
        if (user == null) {
            return Map.of(
                "success", false,
                "message", "用户不存在: " + userId
            );
        }

        // 模拟发送通知
        String notificationId = "notify_" + System.currentTimeMillis();

        Map<String, Object> notification = Map.of(
            "notificationId", notificationId,
            "userId", userId,
            "type", type,
            "title", title != null ? title : "系统通知",
            "content", content,
            "status", "SENT",
            "sendTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        return Map.of(
            "success", true,
            "data", notification,
            "message", "通知发送成功"
        );
    }

    /**
     * 创建用户
     */
    public Map<String, Object> createUser(String name, String phone, String email) {
        logger.info("创建用户: name={}, phone={}, email={}", name, phone, email);

        // 检查手机号是否已存在
        boolean phoneExists = users.values().stream()
                .anyMatch(u -> phone.equals(u.get("phone")));
        if (phoneExists) {
            return Map.of(
                "success", false,
                "message", "手机号已存在: " + phone
            );
        }

        // 检查邮箱是否已存在
        boolean emailExists = users.values().stream()
                .anyMatch(u -> email.equals(u.get("email")));
        if (emailExists) {
            return Map.of(
                "success", false,
                "message", "邮箱已存在: " + email
            );
        }

        // 生成新用户ID
        String userId = "user" + String.format("%03d", users.size() + 1);

        Map<String, Object> newUser = Map.of(
            "userId", userId,
            "name", name,
            "phone", phone,
            "email", email,
            "status", "ACTIVE",
            "createTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        users.put(userId, newUser);

        return Map.of(
            "success", true,
            "data", newUser,
            "message", "用户创建成功"
        );
    }

    /**
     * 更新用户状态
     */
    public Map<String, Object> updateUserStatus(String userId, String status, String reason) {
        logger.info("更新用户状态: userId={}, status={}, reason={}", userId, status, reason);

        Map<String, Object> user = users.get(userId);
        if (user == null) {
            return Map.of(
                "success", false,
                "message", "用户不存在: " + userId
            );
        }

        // 创建新的用户对象（模拟更新）
        Map<String, Object> updatedUser = new HashMap<>(user);
        updatedUser.put("status", status);
        updatedUser.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (reason != null) {
            updatedUser.put("statusChangeReason", reason);
        }

        users.put(userId, updatedUser);

        return Map.of(
            "success", true,
            "data", updatedUser,
            "message", "用户状态更新成功"
        );
    }

    /**
     * 创建订单
     */
    public Map<String, Object> createOrder(String userId, String productName, Double amount, String description) {
        logger.info("创建订单: userId={}, productName={}, amount={}", userId, productName, amount);

        // 检查用户是否存在
        Map<String, Object> user = users.get(userId);
        if (user == null) {
            return Map.of(
                "success", false,
                "message", "用户不存在: " + userId
            );
        }

        // 生成新订单ID
        String orderId = "order" + String.format("%03d", orders.size() + 1);

        Map<String, Object> newOrder = Map.of(
            "orderId", orderId,
            "userId", userId,
            "productName", productName,
            "amount", amount,
            "description", description != null ? description : "",
            "status", "PENDING",
            "createTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            "updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        orders.put(orderId, newOrder);

        return Map.of(
            "success", true,
            "data", newOrder,
            "message", "订单创建成功"
        );
    }

    /**
     * 取消订单
     */
    public Map<String, Object> cancelOrder(String orderId, String reason) {
        logger.info("取消订单: orderId={}, reason={}", orderId, reason);

        Map<String, Object> order = orders.get(orderId);
        if (order == null) {
            return Map.of(
                "success", false,
                "message", "订单不存在: " + orderId
            );
        }

        // 检查订单状态是否可以取消
        String currentStatus = (String) order.get("status");
        if ("DELIVERED".equals(currentStatus) || "CANCELLED".equals(currentStatus)) {
            return Map.of(
                "success", false,
                "message", "订单状态不允许取消: " + currentStatus
            );
        }

        // 创建新的订单对象（模拟更新）
        Map<String, Object> updatedOrder = new HashMap<>(order);
        updatedOrder.put("status", "CANCELLED");
        updatedOrder.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        updatedOrder.put("cancelReason", reason);

        orders.put(orderId, updatedOrder);

        return Map.of(
            "success", true,
            "data", updatedOrder,
            "message", "订单取消成功"
        );
    }

    /**
     * 批量发送通知
     */
    public Map<String, Object> sendBulkNotification(String userIds, String type, String title, String content) {
        logger.info("批量发送通知: userIds={}, type={}, title={}", userIds, type, title);

        String[] userIdArray = userIds.split(",");
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (String userId : userIdArray) {
            userId = userId.trim();
            Map<String, Object> user = users.get(userId);

            if (user == null) {
                results.add(Map.of(
                    "userId", userId,
                    "success", false,
                    "message", "用户不存在"
                ));
                failCount++;
                continue;
            }

            // 模拟发送通知
            String notificationId = "notify_" + System.currentTimeMillis() + "_" + userId;
            Map<String, Object> notification = Map.of(
                "notificationId", notificationId,
                "userId", userId,
                "type", type,
                "title", title != null ? title : "系统通知",
                "content", content,
                "status", "SENT",
                "sendTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            results.add(Map.of(
                "userId", userId,
                "success", true,
                "data", notification
            ));
            successCount++;
        }

        return Map.of(
            "success", true,
            "data", Map.of(
                "totalCount", userIdArray.length,
                "successCount", successCount,
                "failCount", failCount,
                "results", results
            ),
            "message", String.format("批量通知发送完成，成功: %d, 失败: %d", successCount, failCount)
        );
    }

    /**
     * 查询通知历史
     */
    public Map<String, Object> queryNotificationHistory(Map<String, Object> params) {
        logger.info("查询通知历史: {}", params);

        // 模拟通知历史数据
        List<Map<String, Object>> notifications = List.of(
            Map.of(
                "notificationId", "notify_001",
                "userId", "user001",
                "type", "SMS",
                "title", "订单状态更新",
                "content", "您的订单已完成支付",
                "status", "SENT",
                "sendTime", "2024-07-20 10:30:00"
            ),
            Map.of(
                "notificationId", "notify_002",
                "userId", "user001",
                "type", "EMAIL",
                "title", "系统维护通知",
                "content", "系统将于今晚进行维护",
                "status", "SENT",
                "sendTime", "2024-07-19 15:20:00"
            )
        );

        return Map.of(
            "success", true,
            "data", notifications,
            "total", notifications.size(),
            "message", "通知历史查询成功"
        );
    }
}

package com.wanshifu.mcp.tools;

import com.wanshifu.mcp.annotation.McpTool;
import com.wanshifu.mcp.annotation.McpToolParam;
import com.wanshifu.mcp.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单相关MCP工具
 * 
 * <AUTHOR> Team
 */
@Component
public class OrderTools {
    
    @Autowired
    private BusinessService businessService;
    
    @McpTool(
        name = "query_order",
        title = "订单信息查询",
        description = "查询订单信息，支持多种查询条件",
        group = "order",
        permissions = {"order:read"}
    )
    public Map<String, Object> queryOrder(
            @McpToolParam(
                name = "orderId",
                description = "订单ID",
                required = false,
                example = "order001"
            ) String orderId,
            
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = false,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "status",
                description = "订单状态",
                required = false,
                enumValues = {"PENDING", "PAID", "SHIPPED", "DELIVERED", "CANCELLED"},
                example = "PENDING"
            ) String status,
            
            @McpToolParam(
                name = "startDate",
                description = "开始日期 (YYYY-MM-DD)",
                required = false,
                example = "2024-07-01"
            ) String startDate,
            
            @McpToolParam(
                name = "endDate",
                description = "结束日期 (YYYY-MM-DD)",
                required = false,
                example = "2024-07-31"
            ) String endDate
    ) {
        Map<String, Object> params = new HashMap<>();
        if (orderId != null) params.put("orderId", orderId);
        if (userId != null) params.put("userId", userId);
        if (status != null) params.put("status", status);
        if (startDate != null) params.put("startDate", startDate);
        if (endDate != null) params.put("endDate", endDate);
        
        return businessService.queryOrder(params);
    }
    
    @McpTool(
        name = "update_order_status",
        title = "更新订单状态",
        description = "更新订单状态",
        group = "order",
        permissions = {"order:write"},
        requiresConfirmation = true
    )
    public Map<String, Object> updateOrderStatus(
            @McpToolParam(
                name = "orderId",
                description = "订单ID",
                required = true,
                example = "order001"
            ) String orderId,
            
            @McpToolParam(
                name = "status",
                description = "新的订单状态",
                required = true,
                enumValues = {"PENDING", "PAID", "SHIPPED", "DELIVERED", "CANCELLED"},
                example = "PAID"
            ) String status,
            
            @McpToolParam(
                name = "reason",
                description = "状态变更原因",
                required = false,
                example = "用户已完成支付"
            ) String reason
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("status", status);
        if (reason != null) params.put("reason", reason);
        
        return businessService.updateOrderStatus(params);
    }
    
    @McpTool(
        name = "create_order",
        title = "快速下单",
        description = "帮用户通过AI快速发布/下万师傅维修安装等各类订单",
        group = "order",
        permissions = {"order:write"},
        requiresConfirmation = true
    )
    public Map<String, Object> createOrder(
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = true,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "productName",
                description = "产品名称",
                required = true,
                example = "万师傅安装服务"
            ) String productName,
            
            @McpToolParam(
                name = "amount",
                description = "订单金额",
                type = "number",
                required = true,
                example = "299.00"
            ) Double amount,
            
            @McpToolParam(
                name = "description",
                description = "订单描述",
                required = false,
                example = "家具安装服务"
            ) String description
    ) {
        return businessService.createOrder(userId, productName, amount, description);
    }
    
    @McpTool(
        name = "cancel_order",
        title = "取消订单",
        description = "取消指定订单",
        group = "order",
        permissions = {"order:write"},
        requiresConfirmation = true
    )
    public Map<String, Object> cancelOrder(
            @McpToolParam(
                name = "orderId",
                description = "订单ID",
                required = true,
                example = "order001"
            ) String orderId,
            
            @McpToolParam(
                name = "reason",
                description = "取消原因",
                required = true,
                example = "用户主动取消"
            ) String reason
    ) {
        return businessService.cancelOrder(orderId, reason);
    }
}

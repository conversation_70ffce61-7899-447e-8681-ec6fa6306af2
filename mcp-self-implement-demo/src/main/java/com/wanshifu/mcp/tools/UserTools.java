package com.wanshifu.mcp.tools;

import com.wanshifu.mcp.annotation.McpTool;
import com.wanshifu.mcp.annotation.McpToolParam;
import com.wanshifu.mcp.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用户相关MCP工具
 * 
 * <AUTHOR> Team
 */
@Component
public class UserTools {
    
    @Autowired
    private BusinessService businessService;
    
    @McpTool(
        name = "query_user",
        title = "用户信息查询",
        description = "查询用户信息，支持通过用户ID、手机号或邮箱查询",
        group = "user",
        permissions = {"user:read"}
    )
    public Map<String, Object> queryUser(
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = false,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "phone",
                description = "手机号",
                required = false,
                example = "***********"
            ) String phone,
            
            @McpToolParam(
                name = "email",
                description = "邮箱地址",
                required = false,
                example = "<EMAIL>"
            ) String email
    ) {
        Map<String, Object> params = Map.of(
            "userId", userId != null ? userId : "",
            "phone", phone != null ? phone : "",
            "email", email != null ? email : ""
        );
        
        return businessService.queryUser(params);
    }
    
    @McpTool(
        name = "create_user",
        title = "创建用户",
        description = "创建新用户账户",
        group = "user",
        permissions = {"user:write"},
        requiresConfirmation = true
    )
    public Map<String, Object> createUser(
            @McpToolParam(
                name = "name",
                description = "用户姓名",
                required = true,
                example = "张三"
            ) String name,
            
            @McpToolParam(
                name = "phone",
                description = "手机号",
                required = true,
                example = "***********"
            ) String phone,
            
            @McpToolParam(
                name = "email",
                description = "邮箱地址",
                required = true,
                example = "<EMAIL>"
            ) String email
    ) {
        return businessService.createUser(name, phone, email);
    }
    
    @McpTool(
        name = "update_user_status",
        title = "更新用户状态",
        description = "更新用户账户状态",
        group = "user",
        permissions = {"user:write"},
        requiresConfirmation = true
    )
    public Map<String, Object> updateUserStatus(
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = true,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "status",
                description = "新状态",
                required = true,
                enumValues = {"ACTIVE", "INACTIVE", "SUSPENDED"},
                example = "ACTIVE"
            ) String status,
            
            @McpToolParam(
                name = "reason",
                description = "状态变更原因",
                required = false,
                example = "用户申请激活"
            ) String reason
    ) {
        return businessService.updateUserStatus(userId, status, reason);
    }
}

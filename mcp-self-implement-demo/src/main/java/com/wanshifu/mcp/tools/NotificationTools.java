package com.wanshifu.mcp.tools;

import com.wanshifu.mcp.annotation.McpTool;
import com.wanshifu.mcp.annotation.McpToolParam;
import com.wanshifu.mcp.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知相关MCP工具
 * 
 * <AUTHOR> Team
 */
@Component
public class NotificationTools {
    
    @Autowired
    private BusinessService businessService;
    
    @McpTool(
        name = "send_notification",
        title = "发送通知",
        description = "发送通知给用户",
        group = "notification",
        permissions = {"notification:send"},
        requiresConfirmation = true
    )
    public Map<String, Object> sendNotification(
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = true,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "type",
                description = "通知类型",
                required = true,
                enumValues = {"SMS", "EMAIL", "PUSH"},
                example = "SMS"
            ) String type,
            
            @McpToolParam(
                name = "title",
                description = "通知标题",
                required = false,
                example = "订单状态更新"
            ) String title,
            
            @McpToolParam(
                name = "content",
                description = "通知内容",
                required = true,
                example = "您的订单已完成支付，正在处理中"
            ) String content
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("type", type);
        params.put("content", content);
        if (title != null) params.put("title", title);
        
        return businessService.sendNotification(params);
    }
    
    @McpTool(
        name = "send_bulk_notification",
        title = "批量发送通知",
        description = "向多个用户批量发送通知",
        group = "notification",
        permissions = {"notification:send", "notification:bulk"},
        requiresConfirmation = true
    )
    public Map<String, Object> sendBulkNotification(
            @McpToolParam(
                name = "userIds",
                description = "用户ID列表（逗号分隔）",
                required = true,
                example = "user001,user002,user003"
            ) String userIds,
            
            @McpToolParam(
                name = "type",
                description = "通知类型",
                required = true,
                enumValues = {"SMS", "EMAIL", "PUSH"},
                example = "EMAIL"
            ) String type,
            
            @McpToolParam(
                name = "title",
                description = "通知标题",
                required = false,
                example = "系统维护通知"
            ) String title,
            
            @McpToolParam(
                name = "content",
                description = "通知内容",
                required = true,
                example = "系统将于今晚进行维护，预计2小时"
            ) String content
    ) {
        return businessService.sendBulkNotification(userIds, type, title, content);
    }
    
    @McpTool(
        name = "query_notification_history",
        title = "查询通知历史",
        description = "查询用户的通知发送历史",
        group = "notification",
        permissions = {"notification:read"}
    )
    public Map<String, Object> queryNotificationHistory(
            @McpToolParam(
                name = "userId",
                description = "用户ID",
                required = false,
                example = "user001"
            ) String userId,
            
            @McpToolParam(
                name = "type",
                description = "通知类型",
                required = false,
                enumValues = {"SMS", "EMAIL", "PUSH"},
                example = "SMS"
            ) String type,
            
            @McpToolParam(
                name = "startDate",
                description = "开始日期 (YYYY-MM-DD)",
                required = false,
                example = "2024-07-01"
            ) String startDate,
            
            @McpToolParam(
                name = "endDate",
                description = "结束日期 (YYYY-MM-DD)",
                required = false,
                example = "2024-07-31"
            ) String endDate,
            
            @McpToolParam(
                name = "limit",
                description = "返回记录数限制",
                type = "integer",
                required = false,
                defaultValue = "50",
                example = "100"
            ) Integer limit
    ) {
        Map<String, Object> params = new HashMap<>();
        if (userId != null) params.put("userId", userId);
        if (type != null) params.put("type", type);
        if (startDate != null) params.put("startDate", startDate);
        if (endDate != null) params.put("endDate", endDate);
        if (limit != null) params.put("limit", limit);
        
        return businessService.queryNotificationHistory(params);
    }
}

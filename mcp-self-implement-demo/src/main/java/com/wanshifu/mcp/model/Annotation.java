package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.Instant;
import java.util.List;

/**
 * MCP注解模型 - 符合MCP 2025-06-18规范
 * 
 * <AUTHOR> Team
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Annotation {
    
    private List<String> audience;
    private Double priority;
    private Instant lastModified;
    
    public Annotation() {}
    
    public Annotation(List<String> audience, Double priority) {
        this.audience = audience;
        this.priority = priority;
    }
    
    public Annotation(List<String> audience, Double priority, Instant lastModified) {
        this.audience = audience;
        this.priority = priority;
        this.lastModified = lastModified;
    }
    
    // Getters and Setters
    public List<String> getAudience() {
        return audience;
    }
    
    public void setAudience(List<String> audience) {
        this.audience = audience;
    }
    
    public Double getPriority() {
        return priority;
    }
    
    public void setPriority(Double priority) {
        this.priority = priority;
    }
    
    public Instant getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }
}

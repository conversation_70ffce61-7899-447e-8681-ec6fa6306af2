package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * MCP请求模型 - 符合MCP 2025-06-18规范
 *
 * <AUTHOR> Team
 */
public class McpRequest {

    @JsonProperty("jsonrpc")
    @NotBlank(message = "jsonrpc version is required")
    private String jsonrpc = "2.0";

    @NotBlank(message = "method is required")
    private String method;

    private Map<String, Object> params;

    private Object id; // 可以是string或number
    
    public McpRequest() {}

    public McpRequest(String method, Map<String, Object> params, Object id) {
        this.method = method;
        this.params = params;
        this.id = id;
    }

    // Getters and Setters
    public String getJsonrpc() {
        return jsonrpc;
    }

    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }
    
    @Override
    public String toString() {
        return "McpRequest{" +
                "jsonrpc='" + jsonrpc + '\'' +
                ", method='" + method + '\'' +
                ", params=" + params +
                ", id='" + id + '\'' +
                '}';
    }
}

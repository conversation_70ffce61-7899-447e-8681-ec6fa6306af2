package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP响应模型 - 符合MCP 2025-06-18规范
 *
 * <AUTHOR> Team
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class McpResponse {

    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";

    private Object result;

    private McpError error;

    private Object id; // 可以是string或number
    
    public McpResponse() {}

    public McpResponse(Object id) {
        this.id = id;
    }

    public static McpResponse success(Object id, Object result) {
        McpResponse response = new McpResponse(id);
        response.setResult(result);
        return response;
    }

    public static McpResponse error(Object id, int code, String message) {
        McpResponse response = new McpResponse(id);
        response.setError(new McpError(code, message));
        return response;
    }

    public static McpResponse error(Object id, int code, String message, Object data) {
        McpResponse response = new McpResponse(id);
        response.setError(new McpError(code, message, data));
        return response;
    }
    
    // Getters and Setters
    public String getJsonrpc() {
        return jsonrpc;
    }
    
    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }
    
    public Object getResult() {
        return result;
    }
    
    public void setResult(Object result) {
        this.result = result;
    }
    
    public McpError getError() {
        return error;
    }
    
    public void setError(McpError error) {
        this.error = error;
    }
    
    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }
    
    /**
     * MCP错误模型
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class McpError {
        private int code;
        private String message;
        private Object data;
        
        public McpError() {}
        
        public McpError(int code, String message) {
            this.code = code;
            this.message = message;
        }
        
        public McpError(int code, String message, Object data) {
            this.code = code;
            this.message = message;
            this.data = data;
        }
        
        // Getters and Setters
        public int getCode() {
            return code;
        }
        
        public void setCode(int code) {
            this.code = code;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public Object getData() {
            return data;
        }
        
        public void setData(Object data) {
            this.data = data;
        }
    }
}

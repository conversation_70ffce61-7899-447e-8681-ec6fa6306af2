package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * MCP资源模型 - 符合MCP 2025-06-18规范
 * 
 * <AUTHOR> Team
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Resource {
    
    private String uri;
    private String title;
    private String mimeType;
    private String text;
    private String blob;
    private Annotation annotations;
    
    public Resource() {}
    
    public Resource(String uri, String title, String mimeType, String text) {
        this.uri = uri;
        this.title = title;
        this.mimeType = mimeType;
        this.text = text;
    }
    
    // Getters and Setters
    public String getUri() {
        return uri;
    }
    
    public void setUri(String uri) {
        this.uri = uri;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getBlob() {
        return blob;
    }
    
    public void setBlob(String blob) {
        this.blob = blob;
    }
    
    public Annotation getAnnotations() {
        return annotations;
    }
    
    public void setAnnotations(Annotation annotations) {
        this.annotations = annotations;
    }
}

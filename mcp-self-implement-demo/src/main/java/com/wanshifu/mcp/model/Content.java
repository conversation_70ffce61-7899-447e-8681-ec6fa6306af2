package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * MCP内容基类 - 符合MCP 2025-06-18规范
 * 
 * <AUTHOR> Team
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = Content.TextContent.class, name = "text"),
    @JsonSubTypes.Type(value = Content.ImageContent.class, name = "image"),
    @JsonSubTypes.Type(value = Content.AudioContent.class, name = "audio"),
    @JsonSubTypes.Type(value = Content.ResourceLinkContent.class, name = "resource_link"),
    @JsonSubTypes.Type(value = Content.EmbeddedResourceContent.class, name = "resource")
})
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class Content {
    
    private String type;
    private Annotation annotations;
    
    protected Content(String type) {
        this.type = type;
    }
    
    // Getters and Setters
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Annotation getAnnotations() {
        return annotations;
    }
    
    public void setAnnotations(Annotation annotations) {
        this.annotations = annotations;
    }
    
    /**
     * 文本内容
     */
    public static class TextContent extends Content {
        private String text;
        
        public TextContent() {
            super("text");
        }
        
        public TextContent(String text) {
            super("text");
            this.text = text;
        }
        
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
    }
    
    /**
     * 图片内容
     */
    public static class ImageContent extends Content {
        private String data;
        private String mimeType;
        
        public ImageContent() {
            super("image");
        }
        
        public ImageContent(String data, String mimeType) {
            super("image");
            this.data = data;
            this.mimeType = mimeType;
        }
        
        public String getData() {
            return data;
        }
        
        public void setData(String data) {
            this.data = data;
        }
        
        public String getMimeType() {
            return mimeType;
        }
        
        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }
    }
    
    /**
     * 音频内容
     */
    public static class AudioContent extends Content {
        private String data;
        private String mimeType;
        
        public AudioContent() {
            super("audio");
        }
        
        public AudioContent(String data, String mimeType) {
            super("audio");
            this.data = data;
            this.mimeType = mimeType;
        }
        
        public String getData() {
            return data;
        }
        
        public void setData(String data) {
            this.data = data;
        }
        
        public String getMimeType() {
            return mimeType;
        }
        
        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }
    }
    
    /**
     * 资源链接内容
     */
    public static class ResourceLinkContent extends Content {
        private String uri;
        private String name;
        private String description;
        private String mimeType;
        
        public ResourceLinkContent() {
            super("resource_link");
        }
        
        public ResourceLinkContent(String uri, String name, String description, String mimeType) {
            super("resource_link");
            this.uri = uri;
            this.name = name;
            this.description = description;
            this.mimeType = mimeType;
        }
        
        public String getUri() {
            return uri;
        }
        
        public void setUri(String uri) {
            this.uri = uri;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getMimeType() {
            return mimeType;
        }
        
        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }
    }
    
    /**
     * 嵌入式资源内容
     */
    public static class EmbeddedResourceContent extends Content {
        private Resource resource;
        
        public EmbeddedResourceContent() {
            super("resource");
        }
        
        public EmbeddedResourceContent(Resource resource) {
            super("resource");
            this.resource = resource;
        }
        
        public Resource getResource() {
            return resource;
        }
        
        public void setResource(Resource resource) {
            this.resource = resource;
        }
    }
}

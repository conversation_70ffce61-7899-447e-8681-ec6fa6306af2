package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * MCP工具结果模型 - 符合MCP 2025-06-18规范
 * 
 * <AUTHOR> Team
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToolResult {
    
    private List<Content> content;
    private Object structuredContent;
    private Boolean isError;
    
    public ToolResult() {}
    
    public ToolResult(List<Content> content, Boolean isError) {
        this.content = content;
        this.isError = isError;
    }
    
    public ToolResult(List<Content> content, Object structuredContent, Boolean isError) {
        this.content = content;
        this.structuredContent = structuredContent;
        this.isError = isError;
    }
    
    // Getters and Setters
    public List<Content> getContent() {
        return content;
    }
    
    public void setContent(List<Content> content) {
        this.content = content;
    }
    
    public Object getStructuredContent() {
        return structuredContent;
    }
    
    public void setStructuredContent(Object structuredContent) {
        this.structuredContent = structuredContent;
    }
    
    public Boolean getIsError() {
        return isError;
    }
    
    public void setIsError(Boolean isError) {
        this.isError = isError;
    }
}

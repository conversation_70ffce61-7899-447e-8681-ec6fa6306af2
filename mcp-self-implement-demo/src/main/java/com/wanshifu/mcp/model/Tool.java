package com.wanshifu.mcp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;

/**
 * MCP工具定义模型 - 符合MCP 2025-06-18规范
 *
 * <AUTHOR> Team
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Tool {

    private String name;
    private String title;
    private String description;
    private Map<String, Object> inputSchema;
    private Map<String, Object> outputSchema;
    private Map<String, Object> annotations;

    public Tool() {}

    public Tool(String name, String description, Map<String, Object> inputSchema) {
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getInputSchema() {
        return inputSchema;
    }

    public void setInputSchema(Map<String, Object> inputSchema) {
        this.inputSchema = inputSchema;
    }

    public Map<String, Object> getOutputSchema() {
        return outputSchema;
    }

    public void setOutputSchema(Map<String, Object> outputSchema) {
        this.outputSchema = outputSchema;
    }

    public Map<String, Object> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Map<String, Object> annotations) {
        this.annotations = annotations;
    }
    
    @Override
    public String toString() {
        return "Tool{" +
                "name='" + name + '\'' +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", inputSchema=" + inputSchema +
                ", outputSchema=" + outputSchema +
                ", annotations=" + annotations +
                '}';
    }
}

package com.wanshifu.mcp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MCP服务配置属性
 *
 * <AUTHOR> Team
 */
@Component
@ConfigurationProperties(prefix = "mcp")
public class McpProperties {

    /**
     * 协议版本
     */
    private String protocolVersion = "2025-06-18";

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * 工具配置
     */
    private Tools tools = new Tools();

    /**
     * Apollo配置
     */
    private Apollo apollo = new Apollo();

    // Getters and Setters
    public String getProtocolVersion() {
        return protocolVersion;
    }

    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public Security getSecurity() {
        return security;
    }

    public void setSecurity(Security security) {
        this.security = security;
    }

    public Tools getTools() {
        return tools;
    }

    public void setTools(Tools tools) {
        this.tools = tools;
    }

    public Apollo getApollo() {
        return apollo;
    }

    public void setApollo(Apollo apollo) {
        this.apollo = apollo;
    }

    /**
     * 安全配置
     */
    public static class Security {
        private List<String> apiKeys;

        public List<String> getApiKeys() {
            return apiKeys;
        }

        public void setApiKeys(List<String> apiKeys) {
            this.apiKeys = apiKeys;
        }
    }

    /**
     * 工具配置
     */
    public static class Tools {
        private boolean enabled = true;
        private boolean autoScan = true;
        private String basePackages = "com.wanshifu.mcp.tools";
        private RateLimit rateLimit = new RateLimit();

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isAutoScan() {
            return autoScan;
        }

        public void setAutoScan(boolean autoScan) {
            this.autoScan = autoScan;
        }

        public String getBasePackages() {
            return basePackages;
        }

        public void setBasePackages(String basePackages) {
            this.basePackages = basePackages;
        }

        public RateLimit getRateLimit() {
            return rateLimit;
        }

        public void setRateLimit(RateLimit rateLimit) {
            this.rateLimit = rateLimit;
        }

        /**
         * 速率限制配置
         */
        public static class RateLimit {
            private boolean enabled = true;
            private int requestsPerMinute = 100;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public int getRequestsPerMinute() {
                return requestsPerMinute;
            }

            public void setRequestsPerMinute(int requestsPerMinute) {
                this.requestsPerMinute = requestsPerMinute;
            }
        }
    }

    /**
     * Apollo配置
     */
    public static class Apollo {
        private boolean enabled = true;
        private String appId = "mcp-service-demo";
        private String metaServer = "http://localhost:8080";
        private String env = "DEV";
        private String cluster = "default";
        private List<String> namespaces = List.of("application");

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getMetaServer() {
            return metaServer;
        }

        public void setMetaServer(String metaServer) {
            this.metaServer = metaServer;
        }

        public String getEnv() {
            return env;
        }

        public void setEnv(String env) {
            this.env = env;
        }

        public String getCluster() {
            return cluster;
        }

        public void setCluster(String cluster) {
            this.cluster = cluster;
        }

        public List<String> getNamespaces() {
            return namespaces;
        }

        public void setNamespaces(List<String> namespaces) {
            this.namespaces = namespaces;
        }
    }
}

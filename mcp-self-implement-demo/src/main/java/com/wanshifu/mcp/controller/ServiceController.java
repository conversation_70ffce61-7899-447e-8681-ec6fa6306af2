package com.wanshifu.mcp.controller;

import com.wanshifu.mcp.model.McpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.info.BuildProperties;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务内部端点控制器
 * 提供服务配置、健康检查等内部接口
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/services")
public class ServiceController {
    private static final Logger logger = LoggerFactory.getLogger(ServiceController.class);

    @Autowired(required = false)
    private BuildProperties buildProperties;

    /**
     * 服务配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig(@RequestBody McpRequest request) {
        logger.info("调用工具请求: {}", request);
        Map<String, Object> config = new HashMap<>();
        
        // 基本信息
        config.put("service", "万师傅 MCP 服务");
        config.put("version", buildProperties != null ? buildProperties.getVersion() : "1.0.0");
        config.put("timestamp", LocalDateTime.now());
        
        // 协议信息
        config.put("protocol", "MCP 2025-06-18");
        config.put("status", "running");
        
        // 功能特性
        Map<String, Object> features = new HashMap<>();
        features.put("tools", true);
        features.put("resources", false);
        features.put("prompts", false);
        config.put("features", features);
        
        return ResponseEntity.ok(config);
    }

    /**
     * 服务健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("service", "mcp-service");
        
        return ResponseEntity.ok(health);
    }

    /**
     * 服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 运行时信息
        Runtime runtime = Runtime.getRuntime();
        status.put("uptime", System.currentTimeMillis());
        status.put("memory", Map.of(
            "total", runtime.totalMemory(),
            "free", runtime.freeMemory(),
            "used", runtime.totalMemory() - runtime.freeMemory(),
            "max", runtime.maxMemory()
        ));
        
        // JVM 信息
        status.put("jvm", Map.of(
            "version", System.getProperty("java.version"),
            "vendor", System.getProperty("java.vendor"),
            "name", System.getProperty("java.vm.name")
        ));
        
        return ResponseEntity.ok(status);
    }
}

package com.wanshifu.mcp.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanshifu.mcp.core.ToolExecutor;
import com.wanshifu.mcp.core.ToolRegistry;
import com.wanshifu.mcp.model.McpRequest;
import com.wanshifu.mcp.model.McpResponse;
import com.wanshifu.mcp.model.Tool;
import com.wanshifu.mcp.model.ToolResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP控制器 - 符合MCP 2025-06-18规范
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/mcp")
public class McpController {

    private static final Logger logger = LoggerFactory.getLogger(McpController.class);

    private static final String PROTOCOL_VERSION = "2025-06-18";

    @Autowired
    private ToolRegistry toolRegistry;

    @Autowired
    private ToolExecutor toolExecutor;

    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * MCP初始化 - 符合MCP 2025-06-18规范
     */
    @PostMapping("/initialize")
    public ResponseEntity<McpResponse> initialize(@Valid @RequestBody McpRequest request, HttpServletRequest httpRequest) {
        logger.info("MCP初始化请求: {}", request);
        logRequestHeaders(httpRequest);

        try {
            Map<String, Object> params = request.getParams();
            String clientProtocolVersion = null;
            Map<String, Object> clientCapabilities = null;
            Map<String, Object> clientInfo = null;

            if (params != null) {
                clientProtocolVersion = (String) params.get("protocolVersion");
                @SuppressWarnings("unchecked")
                Map<String, Object> capabilities = (Map<String, Object>) params.get("capabilities");
                clientCapabilities = capabilities;
                @SuppressWarnings("unchecked")
                Map<String, Object> info = (Map<String, Object>) params.get("clientInfo");
                clientInfo = info;
            }

            logger.info("客户端协议版本: {}, 能力: {}, 信息: {}",
                       clientProtocolVersion, clientCapabilities, clientInfo);

            Map<String, Object> result = new HashMap<>();
            result.put("protocolVersion", PROTOCOL_VERSION);
            result.put("capabilities", Map.of(
                "tools", Map.of("listChanged", true),
                "logging", Map.of(),
                "resources", Map.of("subscribe", false, "listChanged", false),
                "prompts", Map.of("listChanged", false)
            ));
            result.put("serverInfo", Map.of(
                "name", "Wanshifu MCP Service",
                "title", "万师傅 MCP 服务",
                "version", "1.0.0"
            ));
            result.put("instructions", "万师傅MCP服务提供用户管理、订单处理和通知发送等功能。请使用相应的工具来执行操作。");

            McpResponse response = McpResponse.success(request.getId(), result);
            logger.info("MCP初始化响应: {}", toJsonString(response));
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("MCP初始化失败", e);
            McpResponse errorResponse = McpResponse.error(
                request.getId(),
                -32603,
                "Internal error: " + e.getMessage()
            );
            logger.error("MCP初始化错误响应: {}", toJsonString(errorResponse));
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 初始化完成通知
     */
    @PostMapping("/notifications/initialized")
    public ResponseEntity<Void> initialized(@Valid @RequestBody McpRequest request, HttpServletRequest httpRequest) {
        logger.info("收到初始化完成通知: {}", request);
        logRequestHeaders(httpRequest);
        // 这是一个通知，不需要响应
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取工具列表 - 符合MCP 2025-06-18规范
     */
    @PostMapping("/tools/list")
    public ResponseEntity<McpResponse> listTools(@Valid @RequestBody McpRequest request, HttpServletRequest httpRequest) {
        logger.info("获取工具列表请求: {}", request);
        logRequestHeaders(httpRequest);

        try {
            List<Tool> tools = toolRegistry.getAllTools();

            Map<String, Object> result = new HashMap<>();
            result.put("tools", tools);

            // 支持分页（如果需要）
            Map<String, Object> params = request.getParams();
            if (params != null && params.containsKey("cursor")) {
                // 这里可以实现分页逻辑
                result.put("nextCursor", null); // 示例中没有更多数据
            }

            McpResponse response = McpResponse.success(request.getId(), result);
            logger.info("工具列表响应: 返回{}个工具, 响应内容: {}", tools.size(), toJsonString(response));
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取工具列表失败", e);
            McpResponse errorResponse = McpResponse.error(
                request.getId(),
                -32603,
                "Internal error: " + e.getMessage()
            );
            logger.error("工具列表错误响应: {}", toJsonString(errorResponse));
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 调用工具 - 符合MCP 2025-06-18规范
     */
    @PostMapping("/tools/call")
    public ResponseEntity<McpResponse> callTool(@Valid @RequestBody McpRequest request, HttpServletRequest httpRequest) {
        logger.info("调用工具请求: {}", request);
        logRequestHeaders(httpRequest);

        String toolName = null;
        Map<String, Object> arguments = null;

        try {
            Map<String, Object> params = request.getParams();
            if (params == null) {
                return ResponseEntity.ok(McpResponse.error(
                    request.getId(),
                    -32602,
                    "Invalid params: params is required"
                ));
            }

            toolName = (String) params.get("name");
            @SuppressWarnings("unchecked")
            Map<String, Object> tempArguments = (Map<String, Object>) params.get("arguments");
            arguments = tempArguments;

            if (toolName == null) {
                McpResponse errorResponse = McpResponse.error(
                    request.getId(),
                    -32602,
                    "Invalid params: tool name is required"
                );
                logger.warn("工具调用参数错误: 缺少工具名称, 响应: {}", toJsonString(errorResponse));
                return ResponseEntity.ok(errorResponse);
            }

            ToolRegistry.ToolDefinition toolDef = toolRegistry.getToolDefinition(toolName);
            if (toolDef == null) {
                McpResponse errorResponse = McpResponse.error(
                    request.getId(),
                    -32601,
                    "Method not found: " + toolName
                );
                logger.warn("工具未找到: {}, 响应: {}", toolName, toJsonString(errorResponse));
                return ResponseEntity.ok(errorResponse);
            }

            // 执行工具调用
            logger.info("开始执行工具: {}, 参数: {}", toolName, arguments);
            ToolResult toolResult = toolExecutor.executeTool(toolDef, arguments != null ? arguments : new HashMap<>());

            McpResponse response = McpResponse.success(request.getId(), toolResult);
            logger.info("工具执行完成: {}, 结果: {}, 响应: {}", toolName, toJsonString(toolResult), toJsonString(response));
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("调用工具失败: 工具名={}, 参数={}, 请求={}", toolName, arguments, request.getParams(), e);
            McpResponse errorResponse = McpResponse.error(
                request.getId(),
                -32603,
                "Internal error: " + e.getMessage()
            );
            logger.error("工具调用错误响应: {}", toJsonString(errorResponse));
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 工具列表变更通知
     */
    @PostMapping("/notifications/tools/list_changed")
    public ResponseEntity<Void> toolsListChanged(@Valid @RequestBody McpRequest request, HttpServletRequest httpRequest) {
        logger.info("收到工具列表变更通知: {}", request);
        logRequestHeaders(httpRequest);
        // 这是一个通知，不需要响应
        return ResponseEntity.ok().build();
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health(HttpServletRequest httpRequest) {
        logRequestHeaders(httpRequest);
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "timestamp", System.currentTimeMillis(),
            "service", "Wanshifu MCP Service"
        ));
    }

    /**
     * 将对象转换为JSON字符串用于日志输出
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            logger.warn("对象转JSON失败: {}", e.getMessage());
            return obj != null ? obj.toString() : "null";
        }
    }
    
    /**
     * 打印请求头信息
     */
    private void logRequestHeaders(HttpServletRequest request) {
        logger.info("=== MCP Request Headers ===");
        logger.info("Request URL: {} {}", request.getMethod(), request.getRequestURL());
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            logger.info("Header: {} = {}", headerName, headerValue);
        }
        logger.info("==========================");
    }
}
package com.wanshifu.mcp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * MCP工具注解 - 用于标记MCP工具方法
 * 
 * <AUTHOR> Team
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface McpTool {
    
    /**
     * 工具名称（唯一标识符）
     */
    String name();
    
    /**
     * 工具标题（可选，用于显示）
     */
    String title() default "";
    
    /**
     * 工具描述
     */
    String description();
    
    /**
     * 是否需要用户确认
     */
    boolean requiresConfirmation() default false;
    
    /**
     * 工具分组
     */
    String group() default "default";
    
    /**
     * 权限要求
     */
    String[] permissions() default {};
}

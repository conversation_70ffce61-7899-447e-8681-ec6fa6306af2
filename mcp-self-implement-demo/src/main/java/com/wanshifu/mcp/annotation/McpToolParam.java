package com.wanshifu.mcp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * MCP工具参数注解 - 用于标记工具方法参数
 * 
 * <AUTHOR> Team
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
public @interface McpToolParam {
    
    /**
     * 参数名称
     */
    String name();
    
    /**
     * 参数描述
     */
    String description();
    
    /**
     * 是否必需
     */
    boolean required() default true;
    
    /**
     * 默认值
     */
    String defaultValue() default "";
    
    /**
     * 参数类型（用于JSON Schema生成）
     */
    String type() default "";
    
    /**
     * 枚举值（如果适用）
     */
    String[] enumValues() default {};
    
    /**
     * 示例值
     */
    String example() default "";
}

package com.wanshifu.mcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * MCP Service Application
 *
 * <AUTHOR> Team
 */
@SpringBootApplication
@EnableAsync
@EnableAspectJAutoProxy
@EnableConfigurationProperties
public class McpServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpServiceApplication.class, args);
    }
}

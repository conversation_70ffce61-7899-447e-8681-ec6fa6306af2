server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    threads:
      max: 200
      min-spare: 10

spring:
  application:
    name: mcp-service-demo
  profiles:
    active: dev

  # <PERSON>配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# Apollo配置中心
apollo:
  meta: ${APOLLO_META_SERVER:http://localhost:8080}
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true

# MCP服务配置
mcp:
  # 协议版本
  protocol-version: "2025-06-18"

  security:
    # API密钥配置（生产环境应使用环境变量或配置中心）
    api-keys:
      - demo-api-key-123456
      - wanshifu-api-key-789012

  # 工具配置
  tools:
    enabled: true
    auto-scan: true
    base-packages: com.wanshifu.mcp.tools
    rate-limit:
      enabled: true
      requests-per-minute: 100

  # Apollo配置
  apollo:
    enabled: true
    app-id: mcp-service-demo
    meta-server: ${APOLLO_META_SERVER:http://localhost:8080}
    env: ${APOLLO_ENV:DEV}
    cluster: ${APOLLO_CLUSTER:default}
    namespaces:
      - application

# 日志配置
logging:
  level:
    com.wanshifu.mcp: DEBUG
    com.wanshifu.mcp.security.ApiKeyAuthenticationFilter: INFO
    com.wanshifu.mcp.controller.McpController: INFO
    org.springframework.security: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mcp-service.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
  health:
    defaults:
      enabled: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.wanshifu.mcp: DEBUG
    com.wanshifu.mcp.security.ApiKeyAuthenticationFilter: INFO
    com.wanshifu.mcp.controller.McpController: INFO

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    com.wanshifu.mcp: INFO
    com.wanshifu.mcp.security.ApiKeyAuthenticationFilter: INFO
    com.wanshifu.mcp.controller.McpController: INFO

mcp:
  security:
    # 生产环境应从环境变量或Apollo获取
    api-keys: ${MCP_API_KEYS:}
  apollo:
    enabled: true
    meta-server: ${APOLLO_META_SERVER:http://apollo-config-server:8080}
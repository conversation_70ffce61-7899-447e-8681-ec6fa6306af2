package com.wanshifu.mcp.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanshifu.mcp.model.McpRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * MCP控制器测试
 *
 * <AUTHOR> Team
 */
@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "mcp.security.api-keys[0]=demo-api-key-123456",
    "mcp.security.api-keys[1]=wanshifu-api-key-789012",
    "apollo.bootstrap.enabled=false"
})
public class McpControllerTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @org.junit.jupiter.api.BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .apply(org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity())
                .build();
    }
    
    private static final String VALID_API_KEY = "demo-api-key-123456";
    
    @Test
    public void testInitialize() throws Exception {
        Map<String, Object> params = Map.of(
            "protocolVersion", "2025-06-18",
            "capabilities", Map.of(
                "roots", Map.of("listChanged", true),
                "sampling", Map.of(),
                "elicitation", Map.of()
            ),
            "clientInfo", Map.of(
                "name", "TestClient",
                "version", "1.0.0"
            )
        );
        McpRequest request = new McpRequest("initialize", params, "1");

        mockMvc.perform(post("/mcp/initialize")
                .header("X-API-Key", VALID_API_KEY)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.jsonrpc").value("2.0"))
                .andExpect(jsonPath("$.id").value("1"))
                .andExpect(jsonPath("$.result.protocolVersion").value("2025-06-18"))
                .andExpect(jsonPath("$.result.serverInfo.name").value("Wanshifu MCP Service"));
    }
    
    @Test
    public void testListTools() throws Exception {
        McpRequest request = new McpRequest("tools/list", new HashMap<>(), "2");
        
        mockMvc.perform(post("/mcp/tools/list")
                .header("X-API-Key", VALID_API_KEY)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.jsonrpc").value("2.0"))
                .andExpect(jsonPath("$.id").value("2"))
                .andExpect(jsonPath("$.result.tools").isArray());
    }
    
    @Test
    public void testCallToolQueryUser() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("name", "query_user");
        params.put("arguments", Map.of("userId", "user001"));
        
        McpRequest request = new McpRequest("tools/call", params, "3");
        
        mockMvc.perform(post("/mcp/tools/call")
                .header("X-API-Key", VALID_API_KEY)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.jsonrpc").value("2.0"))
                .andExpect(jsonPath("$.id").value("3"))
                .andExpect(jsonPath("$.result.content").isArray())
                .andExpect(jsonPath("$.result.isError").value(false));
    }
    
    @Test
    public void testCallToolWithInvalidApiKey() throws Exception {
        McpRequest request = new McpRequest("initialize", new HashMap<>(), "4");
        
        mockMvc.perform(post("/mcp/initialize")
                .header("X-API-Key", "invalid-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.error.code").value(-32001))
                .andExpect(jsonPath("$.error.message").value("Unauthorized: Invalid or missing API key"));
    }
    
    @Test
    public void testCallToolWithMissingApiKey() throws Exception {
        McpRequest request = new McpRequest("initialize", new HashMap<>(), "5");
        
        mockMvc.perform(post("/mcp/initialize")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    public void testCallNonExistentTool() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("name", "non_existent_tool");
        params.put("arguments", new HashMap<>());
        
        McpRequest request = new McpRequest("tools/call", params, "6");
        
        mockMvc.perform(post("/mcp/tools/call")
                .header("X-API-Key", VALID_API_KEY)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.jsonrpc").value("2.0"))
                .andExpect(jsonPath("$.id").value("6"))
                .andExpect(jsonPath("$.error.code").value(-32601))
                .andExpect(jsonPath("$.error.message").value("Method not found: non_existent_tool"));
    }
}

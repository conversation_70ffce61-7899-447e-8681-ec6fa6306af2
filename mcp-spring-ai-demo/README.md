# 万师傅 MCP Spring AI Demo

这是一个基于 Spring Boot 3.4.1 的 MCP 服务演示项目，集成了和风天气 API 和万师傅业务服务，展示如何快速构建企业级 MCP 服务。

## 项目状态

✅ **已完成** - 完整的 MCP 服务实现，包含天气服务和万师傅业务功能，具备完整的单元测试覆盖。

## 核心功能

### 🏢 万师傅业务服务
- ✅ 用户管理（查询、创建）
- ✅ 师傅管理（查询师傅信息）
- ✅ 订单管理（创建、查询订单）
- ✅ 通知服务（发送各类通知）
- ✅ 平台统计（获取业务数据）

### 🌤️ 天气服务（基于和风天气 API）
- ✅ 实时天气查询（支持中国城市）
- ✅ 天气预报（3-30天预报）
- ✅ 天气预警信息（官方预警数据）
- ✅ 支持中文城市名称查询
- 
## 技术架构

### 🏗️ 核心技术栈
- **Spring Boot 3.4.1** - 应用框架
- **Java 21** - 编程语言
- **WebFlux** - 响应式 Web 框架
- **Maven 3.9+** - 构建工具

### 📦 依赖管理
- Spring Boot Starter Web - Web 服务
- Spring Boot Starter WebFlux - HTTP 客户端
- Jackson 用于 JSON 处理
- JUnit 5 用于单元测试

## 快速开始

### 环境要求
- Java 21+
- Maven 3.9+


```
### 运行服务

```bash
# 1. 配置 API Key
export QWEATHER_API_KEY=your-qweather-api-key

# 2. 编译项目
mvn clean compile

# 3. 运行应用
mvn spring-boot:run

# 服务将在 8081 端口启动
# 查看健康状态
curl http://localhost:8081/actuator/health
```

### 测试功能

```bash
# 1. 运行单元测试
mvn test

# 2. 测试天气服务
curl -X POST http://localhost:8081/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "getCurrentWeatherByCity",
      "arguments": {"cityName": "北京"}
    },
    "id": "1"
  }'

# 3. 测试万师傅业务服务
curl -X POST http://localhost:8081/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "queryUser",
      "arguments": {"userId": "user001"}
    },
    "id": "2"
  }'
```

## 测试覆盖

### 单元测试统计
- **总测试数**: 20 个
- **成功**: 20 个 ✅
- **跳过**: 4 个 (网络相关的集成测试)

### 测试覆盖范围
- ✅ 应用启动测试
- ✅ 万师傅业务服务测试（用户、师傅、订单、通知、统计）
- ✅ 天气服务测试（实例化、边界条件、错误处理）
- ✅ 网络调用测试（实际 API 调用验证）

## 与 mcp-self-implement-demo 的区别

| 特性 | mcp-self-implement-demo | mcp-spring-ai-demo |
|------|------------------------|-----------------|
| **Spring Boot 版本** | 3.3.5 | 3.4.1 |
| **实现方式** | 自实现 MCP 协议 | 基于 Spring Boot + 自定义注解 |
| **开发复杂度** | 较高，需要手动实现协议细节 | 中等，简化的实现方式 |
| **定制化程度** | 高，完全可控 | 高，灵活的注解配置 |
| **维护成本** | 高，需要跟进协议更新 | 中等，自主控制更新 |
| **外部依赖** | 无外部 API 依赖 | 依赖和风天气 API |
| **数据来源** | 模拟数据 | 真实天气数据 + 模拟业务数据 |
| **端口** | 8080 | 8081 |
| **学习价值** | 深入理解 MCP 协议原理 | 快速上手 MCP 开发 + 外部 API 集成 |

---

**万师傅技术团队** © 2024

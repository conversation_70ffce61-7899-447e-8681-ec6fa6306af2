server:
  port: 8080

spring:
  application:
    name: wanshifu-mcp-spring-ai-demo
  profiles:
    active: dev

  # Spring AI MCP 服务器配置
  ai:
    mcp:
      server:
        name: wanshifu-mcp-server
        version: 1.0.0
        description: 万师傅 MCP Spring AI 演示服务

# 日志配置
logging:
  level:
    com.wanshifu.mcp.spring: INFO
    org.springframework.ai: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/mcp-spring-demo.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
  info:
    build:
      enabled: true
    git:
      enabled: true

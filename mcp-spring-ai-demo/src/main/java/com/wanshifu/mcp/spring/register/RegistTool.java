package com.wanshifu.mcp.spring.register;

import com.wanshifu.mcp.spring.service.WanshifuBusinessService;
import com.wanshifu.mcp.spring.service.WeatherService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: MCP工具注册
 * @date 25/7/2025 下午6:41
 **/
@Configuration
public class RegistTool {

    /**
     * 注册天气服务工具
     */
    @Bean
    public ToolCallbackProvider weatherTools(WeatherService weatherService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(weatherService)
                .build();
    }

    /**
     * 注册万师傅业务服务工具
     */
    @Bean
    public ToolCallbackProvider wanshifuBusinessTools(WanshifuBusinessService businessService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(businessService)
                .build();
    }
}

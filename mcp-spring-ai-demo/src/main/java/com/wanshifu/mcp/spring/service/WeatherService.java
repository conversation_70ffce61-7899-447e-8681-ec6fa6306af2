/*
 * Copyright 2024 万师傅技术团队
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.wanshifu.mcp.spring.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClientException;

/**
 * 天气服务
 * 
 * 基于美国国家气象局 API 提供天气预报和预警信息
 * 
 * <AUTHOR> Team
 */
@Service
public class WeatherService {

    private static final String BASE_URL = "https://api.weather.gov";

    private final RestClient restClient;

    public WeatherService() {
        this.restClient = RestClient.builder()
                .baseUrl(BASE_URL)
                .defaultHeader("Accept", "application/geo+json")
                .defaultHeader("User-Agent", "WanshifuMcpClient/1.0 (<EMAIL>)")
                .build();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Points(@JsonProperty("properties") Props properties) {
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Props(@JsonProperty("forecast") String forecast) {
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Forecast(@JsonProperty("properties") Props properties) {
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Props(@JsonProperty("periods") List<Period> periods) {
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Period(
                @JsonProperty("number") Integer number,
                @JsonProperty("name") String name,
                @JsonProperty("startTime") String startTime,
                @JsonProperty("endTime") String endTime,
                @JsonProperty("isDaytime") Boolean isDayTime,
                @JsonProperty("temperature") Integer temperature,
                @JsonProperty("temperatureUnit") String temperatureUnit,
                @JsonProperty("temperatureTrend") String temperatureTrend,
                @JsonProperty("probabilityOfPrecipitation") Map<String, Object> probabilityOfPrecipitation,
                @JsonProperty("windSpeed") String windSpeed,
                @JsonProperty("windDirection") String windDirection,
                @JsonProperty("icon") String icon,
                @JsonProperty("shortForecast") String shortForecast,
                @JsonProperty("detailedForecast") String detailedForecast) {
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Alert(@JsonProperty("features") List<Feature> features) {

        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Feature(@JsonProperty("properties") Properties properties) {
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Properties(
                @JsonProperty("event") String event,
                @JsonProperty("areaDesc") String areaDesc,
                @JsonProperty("severity") String severity,
                @JsonProperty("description") String description,
                @JsonProperty("instruction") String instruction) {
        }
    }

    /**
     * 根据经纬度获取天气预报
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @return 指定位置的天气预报
     * @throws RestClientException 如果请求失败
     */
    @Tool(description = "根据经纬度获取天气预报信息")
    public String getWeatherForecastByLocation(
            @ToolParam(description = "纬度值，范围 -90 到 90") double latitude,
            @ToolParam(description = "经度值，范围 -180 到 180") double longitude) {

        try {
            var points = restClient.get()
                    .uri("/points/{latitude},{longitude}", latitude, longitude)
                    .retrieve()
                    .body(Points.class);

            if (points == null || points.properties() == null || points.properties().forecast() == null) {
                return "无法获取该位置的天气预报信息，请检查经纬度是否正确";
            }

            var forecast = restClient.get()
                    .uri(points.properties().forecast())
                    .retrieve()
                    .body(Forecast.class);

            if (forecast == null || forecast.properties() == null || forecast.properties().periods() == null) {
                return "天气预报数据获取失败";
            }

            String forecastText = forecast.properties().periods().stream()
                    .limit(5) // 限制显示前5个时段
                    .map(p -> String.format("""
                            %s:
                            温度: %s %s
                            风力: %s %s
                            预报: %s
                            """, 
                            p.name(), 
                            p.temperature(), 
                            p.temperatureUnit(), 
                            p.windSpeed(), 
                            p.windDirection(),
                            p.detailedForecast()))
                    .collect(Collectors.joining("\n"));

            return "📍 位置: " + latitude + ", " + longitude + "\n\n" + forecastText;

        } catch (Exception e) {
            return "获取天气预报失败: " + e.getMessage() + 
                   "\n提示: 该服务仅支持美国境内的经纬度查询";
        }
    }

    /**
     * 获取指定美国州的天气预警信息
     * 
     * @param state 美国州代码，如 CA, NY
     * @return 人类可读的预警信息
     * @throws RestClientException 如果请求失败
     */
    @Tool(description = "获取美国指定州的天气预警信息，输入两位州代码如 CA, NY")
    public String getWeatherAlerts(
            @ToolParam(description = "美国州代码，两位字母如 CA, NY, TX") String state) {
        
        try {
            Alert alert = restClient.get()
                    .uri("/alerts/active/area/{state}", state.toUpperCase())
                    .retrieve()
                    .body(Alert.class);

            if (alert == null || alert.features() == null || alert.features().isEmpty()) {
                return "🟢 " + state.toUpperCase() + " 州当前没有天气预警信息";
            }

            String alertText = alert.features().stream()
                    .limit(10) // 限制显示前10个预警
                    .map(f -> String.format("""
                            🚨 事件: %s
                            📍 区域: %s
                            ⚠️ 严重程度: %s
                            📝 描述: %s
                            💡 建议: %s
                            """,
                            f.properties().event(),
                            f.properties().areaDesc(),
                            f.properties().severity(),
                            f.properties().description(),
                            f.properties().instruction()))
                    .collect(Collectors.joining("\n" + "=".repeat(50) + "\n"));

            return "🌪️ " + state.toUpperCase() + " 州天气预警信息:\n\n" + alertText;

        } catch (Exception e) {
            return "获取天气预警失败: " + e.getMessage() + 
                   "\n提示: 请确保输入正确的美国州代码，如 CA, NY, TX";
        }
    }
}

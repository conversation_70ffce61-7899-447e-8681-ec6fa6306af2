/*
 * Copyright 2024 万师傅技术团队
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.wanshifu.mcp.spring.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

/**
 * 万师傅业务服务
 * 
 * 提供万师傅平台的核心业务功能，包括用户管理、订单处理、师傅管理等
 * 
 * <AUTHOR> Team
 */
@Service
public class WanshifuBusinessService {

    private final Random random = new Random();
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 模拟数据存储
    private final Map<String, Map<String, Object>> users = new HashMap<>();
    private final Map<String, Map<String, Object>> orders = new HashMap<>();
    private final Map<String, Map<String, Object>> masters = new HashMap<>();

    public WanshifuBusinessService() {
        initMockData();
    }

    /**
     * 初始化模拟数据
     */
    private void initMockData() {
        // 模拟用户数据
        users.put("user001", Map.of(
            "id", "user001",
            "name", "张三",
            "phone", "***********",
            "email", "<EMAIL>",
            "address", "北京市朝阳区建国路88号",
            "status", "ACTIVE",
            "createTime", "2024-01-15 10:30:00"
        ));

        users.put("user002", Map.of(
            "id", "user002", 
            "name", "李四",
            "phone", "***********",
            "email", "<EMAIL>",
            "address", "上海市浦东新区陆家嘴金融中心",
            "status", "ACTIVE",
            "createTime", "2024-02-20 14:20:00"
        ));

        // 模拟师傅数据
        masters.put("master001", Map.of(
            "id", "master001",
            "name", "王师傅",
            "phone", "***********",
            "skills", List.of("家电维修", "空调安装", "洗衣机维修"),
            "rating", 4.8,
            "location", "北京市朝阳区",
            "status", "AVAILABLE",
            "experience", "8年经验"
        ));

        masters.put("master002", Map.of(
            "id", "master002",
            "name", "刘师傅", 
            "phone", "13900139002",
            "skills", List.of("家具安装", "灯具安装", "卫浴安装"),
            "rating", 4.9,
            "location", "上海市浦东新区",
            "status", "BUSY",
            "experience", "12年经验"
        ));

        // 模拟订单数据
        orders.put("ORDER001", Map.of(
            "id", "ORDER001",
            "userId", "user001",
            "masterId", "master001",
            "serviceType", "空调维修",
            "description", "空调不制冷，需要检查维修",
            "amount", 200.0,
            "status", "COMPLETED",
            "createTime", "2024-07-20 09:00:00",
            "completeTime", "2024-07-20 11:30:00"
        ));
    }

    /**
     * 查询用户信息
     */
    @Tool(description = "根据用户ID查询用户详细信息")
    public String queryUser(@ToolParam(description = "用户唯一标识符") String userId) {
        Map<String, Object> user = users.get(userId);
        
        if (user == null) {
            return "❌ 用户不存在: " + userId;
        }

        return String.format("""
            👤 用户信息:
            ID: %s
            姓名: %s
            手机: %s
            邮箱: %s
            地址: %s
            状态: %s
            注册时间: %s
            """, 
            user.get("id"), user.get("name"), user.get("phone"), 
            user.get("email"), user.get("address"), user.get("status"), 
            user.get("createTime"));
    }

    /**
     * 创建新用户
     */
    @Tool(description = "创建新用户账户")
    public String createUser(
            @ToolParam(description = "用户姓名") String name,
            @ToolParam(description = "手机号码") String phone,
            @ToolParam(description = "邮箱地址") String email,
            @ToolParam(description = "用户地址") String address) {
        
        String userId = "user" + String.format("%03d", users.size() + 1);
        
        Map<String, Object> newUser = new HashMap<>();
        newUser.put("id", userId);
        newUser.put("name", name);
        newUser.put("phone", phone);
        newUser.put("email", email);
        newUser.put("address", address);
        newUser.put("status", "ACTIVE");
        newUser.put("createTime", LocalDateTime.now().format(formatter));
        
        users.put(userId, newUser);
        
        return String.format("""
            ✅ 用户创建成功!
            用户ID: %s
            姓名: %s
            手机: %s
            邮箱: %s
            地址: %s
            创建时间: %s
            """, 
            userId, name, phone, email, address, newUser.get("createTime"));
    }

    /**
     * 查询师傅信息
     */
    @Tool(description = "根据师傅ID查询师傅详细信息")
    public String queryMaster(@ToolParam(description = "师傅唯一标识符") String masterId) {
        Map<String, Object> master = masters.get(masterId);
        
        if (master == null) {
            return "❌ 师傅不存在: " + masterId;
        }

        return String.format("""
            🔧 师傅信息:
            ID: %s
            姓名: %s
            手机: %s
            技能: %s
            评分: %.1f/5.0
            位置: %s
            状态: %s
            经验: %s
            """, 
            master.get("id"), master.get("name"), master.get("phone"),
            master.get("skills"), master.get("rating"), master.get("location"),
            master.get("status"), master.get("experience"));
    }

    /**
     * 创建服务订单
     */
    @Tool(description = "创建新的服务订单")
    public String createOrder(
            @ToolParam(description = "用户ID") String userId,
            @ToolParam(description = "服务类型，如：空调维修、家具安装、灯具安装") String serviceType,
            @ToolParam(description = "服务描述") String description,
            @ToolParam(description = "服务金额") double amount) {
        
        if (!users.containsKey(userId)) {
            return "❌ 用户不存在: " + userId;
        }
        
        String orderId = "ORDER" + String.format("%03d", orders.size() + 1);
        
        Map<String, Object> newOrder = new HashMap<>();
        newOrder.put("id", orderId);
        newOrder.put("userId", userId);
        newOrder.put("serviceType", serviceType);
        newOrder.put("description", description);
        newOrder.put("amount", amount);
        newOrder.put("status", "PENDING");
        newOrder.put("createTime", LocalDateTime.now().format(formatter));
        
        orders.put(orderId, newOrder);
        
        return String.format("""
            ✅ 订单创建成功!
            订单ID: %s
            用户ID: %s
            服务类型: %s
            服务描述: %s
            服务金额: ¥%.2f
            订单状态: 待处理
            创建时间: %s
            """, 
            orderId, userId, serviceType, description, amount, newOrder.get("createTime"));
    }

    /**
     * 查询订单信息
     */
    @Tool(description = "根据订单ID查询订单详细信息")
    public String queryOrder(@ToolParam(description = "订单唯一标识符") String orderId) {
        Map<String, Object> order = orders.get(orderId);
        
        if (order == null) {
            return "❌ 订单不存在: " + orderId;
        }

        return String.format("""
            📋 订单信息:
            订单ID: %s
            用户ID: %s
            师傅ID: %s
            服务类型: %s
            服务描述: %s
            服务金额: ¥%.2f
            订单状态: %s
            创建时间: %s
            完成时间: %s
            """, 
            order.get("id"), order.get("userId"), order.get("masterId"),
            order.get("serviceType"), order.get("description"), order.get("amount"),
            order.get("status"), order.get("createTime"), 
            order.getOrDefault("completeTime", "未完成"));
    }

    /**
     * 发送通知
     */
    @Tool(description = "向用户发送通知消息")
    public String sendNotification(
            @ToolParam(description = "用户ID") String userId,
            @ToolParam(description = "通知类型：SMS, EMAIL, PUSH") String type,
            @ToolParam(description = "通知标题") String title,
            @ToolParam(description = "通知内容") String content) {
        
        if (!users.containsKey(userId)) {
            return "❌ 用户不存在: " + userId;
        }
        
        Map<String, Object> user = users.get(userId);
        String notificationId = "NOTIFY" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        
        return String.format("""
            📨 通知发送成功!
            通知ID: %s
            接收用户: %s (%s)
            通知类型: %s
            通知标题: %s
            通知内容: %s
            发送时间: %s
            """, 
            notificationId, user.get("name"), userId, type, title, content,
            LocalDateTime.now().format(formatter));
    }

    /**
     * 获取平台统计信息
     */
    @Tool(description = "获取万师傅平台的统计信息")
    public String getPlatformStats() {
        return String.format("""
            📊 万师傅平台统计信息:
            
            👥 用户统计:
            - 总用户数: %d
            - 活跃用户: %d
            
            🔧 师傅统计:
            - 总师傅数: %d
            - 在线师傅: %d
            
            📋 订单统计:
            - 总订单数: %d
            - 已完成订单: %d
            - 进行中订单: %d
            
            💰 业务统计:
            - 平台总交易额: ¥%.2f
            - 平均订单金额: ¥%.2f
            
            📈 更新时间: %s
            """, 
            users.size(), users.size(),
            masters.size(), 1,
            orders.size(), 1, 0,
            200.0, 200.0,
            LocalDateTime.now().format(formatter));
    }
}

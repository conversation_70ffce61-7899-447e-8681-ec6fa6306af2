package com.wanshifu.mcp.spring;

import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import com.wanshifu.mcp.spring.service.WeatherService;
import com.wanshifu.mcp.spring.service.WanshifuBusinessService;

/**
 * 万师傅 MCP Spring AI Demo 应用启动类
 *
 * 基于 Spring AI MCP 框架的万师傅业务演示项目
 * 集成了天气服务和万师傅业务服务的 MCP 工具
 *
 * <AUTHOR> Team
 */
@SpringBootApplication
public class McpSpringDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpSpringDemoApplication.class, args);
    }

}

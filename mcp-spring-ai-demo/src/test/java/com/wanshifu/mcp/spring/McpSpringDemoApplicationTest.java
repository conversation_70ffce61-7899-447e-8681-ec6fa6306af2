package com.wanshifu.mcp.spring;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 万师傅 MCP Spring AI Demo 应用测试
 * 
 * <AUTHOR> Team
 */
@SpringBootTest
@TestPropertySource(properties = {
    "logging.level.com.wanshifu.mcp.spring=DEBUG"
})
class McpSpringDemoApplicationTest {

    @Test
    void contextLoads() {
        // 测试 Spring 上下文是否能正常加载
        System.out.println("✅ Spring 上下文加载成功!");
    }
}

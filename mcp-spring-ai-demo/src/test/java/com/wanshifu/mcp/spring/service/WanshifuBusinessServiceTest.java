package com.wanshifu.mcp.spring.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 万师傅业务服务测试
 * 
 * <AUTHOR> Team
 */
class WanshifuBusinessServiceTest {

    private WanshifuBusinessService businessService;

    @BeforeEach
    void setUp() {
        businessService = new WanshifuBusinessService();
    }

    @Test
    @DisplayName("测试查询存在的用户")
    void testQueryExistingUser() {
        String result = businessService.queryUser("user001");
        
        assertNotNull(result);
        assertTrue(result.contains("张三"));
        assertTrue(result.contains("***********"));
        assertTrue(result.contains("<EMAIL>"));
        
        System.out.println("✅ 查询用户测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试查询不存在的用户")
    void testQueryNonExistentUser() {
        String result = businessService.queryUser("user999");
        
        assertNotNull(result);
        assertTrue(result.contains("❌ 用户不存在"));
        
        System.out.println("✅ 查询不存在用户测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试创建新用户")
    void testCreateUser() {
        String result = businessService.createUser(
            "测试用户", 
            "***********", 
            "<EMAIL>", 
            "北京市海淀区"
        );
        
        assertNotNull(result);
        assertTrue(result.contains("✅ 用户创建成功"));
        assertTrue(result.contains("测试用户"));
        assertTrue(result.contains("***********"));
        
        System.out.println("✅ 创建用户测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试查询师傅信息")
    void testQueryMaster() {
        String result = businessService.queryMaster("master001");
        
        assertNotNull(result);
        assertTrue(result.contains("王师傅"));
        assertTrue(result.contains("家电维修"));
        assertTrue(result.contains("4.8"));
        
        System.out.println("✅ 查询师傅测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试创建订单")
    void testCreateOrder() {
        String result = businessService.createOrder(
            "user001", 
            "洗衣机维修", 
            "洗衣机不能脱水", 
            150.0
        );
        
        assertNotNull(result);
        assertTrue(result.contains("✅ 订单创建成功"));
        assertTrue(result.contains("洗衣机维修"));
        assertTrue(result.contains("150.00"));
        
        System.out.println("✅ 创建订单测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试查询订单")
    void testQueryOrder() {
        String result = businessService.queryOrder("ORDER001");
        
        assertNotNull(result);
        assertTrue(result.contains("ORDER001"));
        assertTrue(result.contains("空调维修"));
        assertTrue(result.contains("200.00"));
        
        System.out.println("✅ 查询订单测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试发送通知")
    void testSendNotification() {
        String result = businessService.sendNotification(
            "user001", 
            "SMS", 
            "订单状态更新", 
            "您的订单已完成"
        );
        
        assertNotNull(result);
        assertTrue(result.contains("📨 通知发送成功"));
        assertTrue(result.contains("订单状态更新"));
        assertTrue(result.contains("SMS"));
        
        System.out.println("✅ 发送通知测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试获取平台统计")
    void testGetPlatformStats() {
        String result = businessService.getPlatformStats();
        
        assertNotNull(result);
        assertTrue(result.contains("📊 万师傅平台统计信息"));
        assertTrue(result.contains("总用户数"));
        assertTrue(result.contains("总师傅数"));
        assertTrue(result.contains("总订单数"));
        
        System.out.println("✅ 平台统计测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试创建订单时用户不存在")
    void testCreateOrderWithNonExistentUser() {
        String result = businessService.createOrder(
            "user999", 
            "测试服务", 
            "测试描述", 
            100.0
        );
        
        assertNotNull(result);
        assertTrue(result.contains("❌ 用户不存在"));
        
        System.out.println("✅ 用户不存在创建订单测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试发送通知时用户不存在")
    void testSendNotificationWithNonExistentUser() {
        String result = businessService.sendNotification(
            "user999", 
            "EMAIL", 
            "测试通知", 
            "测试内容"
        );
        
        assertNotNull(result);
        assertTrue(result.contains("❌ 用户不存在"));
        
        System.out.println("✅ 用户不存在发送通知测试通过:");
        System.out.println(result);
    }
}

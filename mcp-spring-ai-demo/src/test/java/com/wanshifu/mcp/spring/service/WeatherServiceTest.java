package com.wanshifu.mcp.spring.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Disabled;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 天气服务测试
 * 
 * 注意：这些测试需要网络连接到美国国家气象局API
 * 
 * <AUTHOR> Team
 */
class WeatherServiceTest {

    private WeatherService weatherService;

    @BeforeEach
    void setUp() {
        weatherService = new WeatherService();
    }

    @Test
    @DisplayName("测试天气服务实例化")
    void testWeatherServiceInstantiation() {
        assertNotNull(weatherService);
        System.out.println("✅ 天气服务实例化成功");
    }

    @Test
    @Disabled("需要网络连接，集成测试时启用")
    @DisplayName("测试获取西雅图天气预报")
    void testGetWeatherForecastForSeattle() {
        // 西雅图坐标: 47.6062, -122.3321
        String result = weatherService.getWeatherForecastByLocation(47.6062, -122.3321);
        
        assertNotNull(result);
        assertTrue(result.contains("位置: 47.6062, -122.3321"));
        
        System.out.println("✅ 西雅图天气预报测试通过:");
        System.out.println(result);
    }

    @Test
    @Disabled("需要网络连接，集成测试时启用")
    @DisplayName("测试获取纽约天气预报")
    void testGetWeatherForecastForNewYork() {
        // 纽约坐标: 40.7128, -74.0060
        String result = weatherService.getWeatherForecastByLocation(40.7128, -74.0060);
        
        assertNotNull(result);
        assertTrue(result.contains("位置: 40.7128, -74.006"));
        
        System.out.println("✅ 纽约天气预报测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试无效坐标的天气预报")
    void testGetWeatherForecastForInvalidLocation() {
        // 使用中国北京的坐标（美国气象局API不支持）
        String result = weatherService.getWeatherForecastByLocation(39.9042, 116.4074);
        
        assertNotNull(result);
        assertTrue(result.contains("获取天气预报失败") || result.contains("仅支持美国境内"));
        
        System.out.println("✅ 无效坐标天气预报测试通过:");
        System.out.println(result);
    }

    @Test
    @Disabled("需要网络连接，集成测试时启用")
    @DisplayName("测试获取加州天气预警")
    void testGetWeatherAlertsForCalifornia() {
        String result = weatherService.getWeatherAlerts("CA");
        
        assertNotNull(result);
        assertTrue(result.contains("CA 州"));
        
        System.out.println("✅ 加州天气预警测试通过:");
        System.out.println(result);
    }

    @Test
    @Disabled("需要网络连接，集成测试时启用")
    @DisplayName("测试获取纽约州天气预警")
    void testGetWeatherAlertsForNewYork() {
        String result = weatherService.getWeatherAlerts("NY");
        
        assertNotNull(result);
        assertTrue(result.contains("NY 州"));
        
        System.out.println("✅ 纽约州天气预警测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试无效州代码的天气预警")
    void testGetWeatherAlertsForInvalidState() {
        String result = weatherService.getWeatherAlerts("XX");
        
        assertNotNull(result);
        assertTrue(result.contains("获取天气预警失败") || result.contains("XX 州当前没有天气预警"));
        
        System.out.println("✅ 无效州代码天气预警测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试小写州代码")
    void testGetWeatherAlertsWithLowerCase() {
        // 测试小写输入是否能正确转换为大写
        String result = weatherService.getWeatherAlerts("ca");
        
        assertNotNull(result);
        assertTrue(result.contains("CA 州"));
        
        System.out.println("✅ 小写州代码测试通过:");
        System.out.println(result);
    }

    @Test
    @DisplayName("测试边界坐标值")
    void testBoundaryCoordinates() {
        // 测试边界值
        String result1 = weatherService.getWeatherForecastByLocation(90.0, 180.0);
        String result2 = weatherService.getWeatherForecastByLocation(-90.0, -180.0);
        String result3 = weatherService.getWeatherForecastByLocation(0.0, 0.0);
        
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        
        System.out.println("✅ 边界坐标测试通过");
        System.out.println("北极点结果: " + result1.substring(0, Math.min(100, result1.length())));
        System.out.println("南极点结果: " + result2.substring(0, Math.min(100, result2.length())));
        System.out.println("赤道本初子午线结果: " + result3.substring(0, Math.min(100, result3.length())));
    }
}

# 万师傅 MCP 服务

[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/projects/jdk/21/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.3.5-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![MCP Protocol](https://img.shields.io/badge/MCP-2025--06--18-blue.svg)](https://modelcontextprotocol.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 企业级 MCP (Model Context Protocol) 服务实现，安全地将内部业务系统能力开放给 AI 应用使用

## 项目结构

本项目采用 Maven 多模块架构，包含以下模块：

### 📦 模块说明

| 模块 | 描述 | 状态 | 技术栈 |
|------|------|------|--------|
| **mcp-self-implement-demo** | 自实现的 MCP 服务 | ✅ 完整实现 | Spring Boot 3.3.5 + 自定义 MCP 协议 |
| **mcp-spring-ai-demo** | 基于 Spring AI 的 MCP 服务 | ✅ 完整实现 | Spring Boot 3.4.1 + 天气 API |

### 🏗️ 项目架构

```
mcp-service-demo/
├── mcp-self-implement-demo/          # 自实现 MCP 服务模块
│   ├── src/                          # 源代码
│   ├── docker/                       # Docker 配置
│   ├── scripts/                      # 部署脚本
│   └── pom.xml                       # 模块配置
├── mcp-spring-ai-demo/               # Spring AI MCP 服务模块
│   ├── src/                          # 源代码（完整实现）
│   └── pom.xml                       # 模块配置
├── docs/                             # 项目文档
├── pom.xml                           # 父项目配置
└── README.md                         # 项目说明
```

## 概述

万师傅 MCP 服务是一个基于 Spring Boot 和 Java 21 构建的企业级 MCP 协议实现，完全符合 **MCP 2025-06-18** 最新规范。该项目提供了两种不同的实现方式，展示了如何安全、可扩展地让 AI 应用访问和操作内部业务系统。

### 🏗️ 双重实现架构

#### **mcp-self-implement-demo** - 自实现方案
- **技术栈**: Spring Boot 3.3.5 + 自定义 MCP 协议实现
- **特点**: 完全自主可控，深度定制化
- **适用场景**: 需要精确控制协议细节的企业级应用

#### **mcp-spring-ai-demo** - Spring AI 方案
- **技术栈**: Spring Boot 3.4.1 
- **特点**: 基于官方框架，快速开发
- **适用场景**: 快速原型开发和标准化实现

### 核心特性

#### 🔐 企业级安全
- **多重认证机制**: API Key + Bearer Token 双重认证支持
- **细粒度权限控制**: 基于角色的访问控制 (RBAC)
- **请求审计**: 完整的 API 调用日志和审计追踪
- **安全防护**: 内置防护机制，防止数据泄露和恶意攻击

#### 🛠️ 丰富的业务工具
- **用户管理**: 用户信息查询、创建、更新和状态管理
- **订单系统**: 订单全生命周期管理和状态追踪
- **通知服务**: 多渠道通知发送和历史查询
- **可扩展架构**: 基于注解的工具注册，轻松添加新功能

#### 🏗️ 现代化架构
- **注解驱动**: `@McpTool` 注解自动发现和注册
- **配置中心**: Apollo 配置中心集成，支持动态配置
- **监控就绪**: Spring Boot Actuator 集成，完整的健康检查和指标监控
- **容器化**: Docker 支持，云原生部署就绪

## 快速开始

### 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Java | 21+ | 推荐使用 OpenJDK 21 LTS |
| Maven | 3.9+ | 构建工具（已升级支持） |
| Docker | 20.10+ | 容器化部署（可选） |
| Apollo | 2.3.0+ | 配置中心（可选） |

### 安装部署

#### 方式一：一键部署（推荐）

```bash
# 克隆项目
git clone http://git.wanshifu.com/userplatform/mcp-server-demo.git
cd mcp-service-demo

# 一键升级部署（自动安装 JDK 21 + 构建 + 部署）
./scripts/upgrade-deploy.sh
```

#### 方式二：手动部署

**运行自实现 MCP 服务 (推荐)**:
```bash
# 1. 编译整个项目
mvn clean compile

# 2. 运行自实现 MCP 服务
cd mcp-self-implement-demo
mvn spring-boot:run

# 或者打包后运行
mvn clean package -DskipTests
java -jar target/mcp-self-implement-demo-1.0.0-SNAPSHOT.jar
```

**运行 Spring AI MCP 服务**:
```bash

# 1. 进入 Spring AI MCP 模块
cd mcp-spring-ai-demo

# 2. 运行服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package -DskipTests
java -jar target/mcp-spring-ai-demo-1.0.0-SNAPSHOT.jar
```

#### 方式三：Docker 部署

**部署自实现 MCP 服务**:
```bash
# 构建镜像
docker build -f mcp-self-implement-demo/docker/Dockerfile -t mcp-self-implement:latest .

# 启动容器
docker run -d \
  --name mcp-self-implement \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  mcp-self-implement:latest
```

**部署 Spring AI MCP 服务**:
```bash
# 构建镜像
docker build -f mcp-spring-ai-demo/docker/Dockerfile -t mcp-spring-ai:latest .

# 启动容器
docker run -d \
  --name mcp-spring-ai \
  -p 8081:8081 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e QWEATHER_API_KEY=your-qweather-api-key \
  mcp-spring-ai:latest
```

## 📊 功能对比

### 两种实现方案对比

| 特性 | mcp-self-implement-demo | mcp-spring-ai-demo  |
|------|------------------------|---------------------|
| **Spring Boot 版本** | 3.3.5 | 3.4.1               |
| **实现方式** | 自实现 MCP 协议 | 基于 Spring AI 框架     |
| **开发复杂度** | 较高，需要手动实现协议细节 | 较低，框架提供标准实现         |
| **定制化程度** | 高，完全可控 | 中等，受框架限制            |
| **维护成本** | 高，需要跟进协议更新 | 低，框架自动更新            |
| **学习价值** | 深入理解 MCP 协议原理 | 快速上手 Spring AI MCP 开发 |
| **业务功能** | 万师傅完整业务场景 | 天气服务 + 万师傅业务        |
| **外部 API** | 模拟数据 | 天气真实 API            |
| **端口** | 8080 | 8080                |

### 功能特性详情

#### **mcp-self-implement-demo** 功能
- ✅ 用户管理（查询、创建、更新）
- ✅ 师傅管理（查询师傅信息、技能匹配）
- ✅ 订单管理（创建、查询、状态更新）
- ✅ 通知服务（SMS、邮件、推送）
- ✅ 平台统计（业务数据分析）
- ✅ 完整的 MCP 协议实现
- ✅ 企业级安全认证
- ✅ 详细的日志和监控

#### **mcp-spring-ai-demo** 功能
- ✅ 实时天气查询（天气 API）
- ✅ 天气预报（3-30天预报）
- ✅ 天气预警信息
- ✅ 万师傅业务服务（用户、订单、师傅管理）
- ✅ 文本处理工具（大小写转换）
- ✅ Spring AI 框架集成
- ✅ 完整的单元测试覆盖

### 服务验证

部署完成后，可以通过以下方式验证服务状态：

#### **验证 mcp-self-implement-demo (端口 8080)**

```bash
# 1. 健康检查
curl http://localhost:8080/actuator/health

# 2. MCP 协议初始化
curl -X POST http://localhost:8080/mcp/initialize \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-06-18",
      "capabilities": {
        "roots": {"listChanged": true},
        "sampling": {},
        "elicitation": {}
      },
      "clientInfo": {
        "name": "TestClient",
        "version": "1.0.0"
      }
    },
    "id": "1"
  }'

# 3. 测试业务工具调用
curl -X POST http://localhost:8080/mcp/tools/call \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "query_user",
      "arguments": {"userId": "user001"}
    },
    "id": "2"
  }'
```

#### **验证 mcp-spring-ai-demo (端口 8081)**

```bash
# 1. 健康检查
curl http://localhost:8081/actuator/health

# 2. 测试天气服务
curl -X POST http://localhost:8081/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "getCurrentWeatherByCity",
      "arguments": {"cityName": "北京"}
    },
    "id": "3"
  }'

# 3. 测试万师傅业务服务
curl -X POST http://localhost:8081/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "queryUser",
      "arguments": {"userId": "user001"}
    },
    "id": "4"
  }'
```

预期响应：
```json
{
  "jsonrpc": "2.0",
  "result": {
    "protocolVersion": "2025-06-18",
    "capabilities": {
      "tools": {"listChanged": true},
      "resources": {"subscribe": true, "listChanged": true}
    },
    "serverInfo": {
      "name": "万师傅 MCP 服务",
      "version": "1.0.0"
    }
  },
  "id": "1"
}
```

## AI 客户端接入指南

万师傅 MCP 服务支持多种 AI 客户端接入，包括 Claude Desktop、Cursor IDE、通义 Lingma 等主流 AI 开发工具。以下是详细的接入配置教程。

### Claude Desktop 接入

Claude Desktop 是 Anthropic 官方提供的桌面应用，支持 MCP 协议。

#### 1. 安装 Claude Desktop

**macOS**:
```bash
# 下载并安装 Claude Desktop
# 访问 https://claude.ai/download 下载最新版本
```

**Windows**:
```bash
# 下载并安装 Claude Desktop
# 访问 https://claude.ai/download 下载最新版本
```

#### 2. 配置 MCP 服务

在 Claude Desktop 中配置万师傅 MCP 服务：

**macOS 配置文件位置**: `~/Library/Application Support/Claude/claude_desktop_config.json`

**Windows 配置文件位置**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "wanshifu-mcp": {
      "command": "node",
      "args": ["/path/to/mcp-client-bridge.js"],
      "env": {
        "MCP_SERVER_URL": "https://your-mcp-server.com/mcp",
        "MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

#### 3. 创建客户端桥接脚本

创建 `mcp-client-bridge.js` 文件：

```javascript
#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const axios = require('axios');

class WanshifuMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: "wanshifu-mcp-server",
        version: "1.0.0",
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.mcpServerUrl = process.env.MCP_SERVER_URL;
    this.apiKey = process.env.MCP_API_KEY;

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      try {
        const response = await axios.post(`${this.mcpServerUrl}/tools/list`, {
          jsonrpc: "2.0",
          method: "tools/list",
          id: Date.now()
        }, {
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          }
        });

        return {
          tools: response.data.result.tools
        };
      } catch (error) {
        console.error('Failed to list tools:', error);
        return { tools: [] };
      }
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        const response = await axios.post(`${this.mcpServerUrl}/tools/call`, {
          jsonrpc: "2.0",
          method: "tools/call",
          params: {
            name: request.params.name,
            arguments: request.params.arguments
          },
          id: Date.now()
        }, {
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          }
        });

        return {
          content: response.data.result.content,
          isError: response.data.result.isError || false
        };
      } catch (error) {
        console.error('Failed to call tool:', error);
        return {
          content: [{ type: "text", text: `Error: ${error.message}` }],
          isError: true
        };
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Wanshifu MCP server running on stdio");
  }
}

const server = new WanshifuMCPServer();
server.run().catch(console.error);
```

#### 4. 安装依赖

```bash
npm install @modelcontextprotocol/sdk axios
```

### Cursor IDE 接入

Cursor 是一款 AI 驱动的代码编辑器，支持 MCP 协议集成。

#### 1. 安装 Cursor

```bash
# 访问 https://cursor.sh/ 下载并安装 Cursor
```

#### 2. 配置 MCP 扩展

在 Cursor 中安装 MCP 扩展：

1. 打开 Cursor
2. 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows)
3. 搜索 "Extensions: Install Extensions"
4. 搜索并安装 "MCP Protocol Support"

#### 3. 配置万师傅 MCP 服务

在 Cursor 设置中配置 MCP 服务：

**设置路径**: `Preferences > Extensions > MCP Protocol`

```json
{
  "mcp.servers": [
    {
      "name": "万师傅业务服务",
      "url": "https://your-mcp-server.com/mcp",
      "apiKey": "your-api-key-here",
      "description": "万师傅内部业务系统 MCP 服务",
      "enabled": true,
      "timeout": 30000
    }
  ]
}
```

#### 4. 使用 MCP 工具

在 Cursor 中使用万师傅 MCP 工具：

```typescript
// 在代码中使用 MCP 工具
// Cursor 会自动识别并提供智能提示

// 查询用户信息
const user = await mcp.call('query_user', { userId: 'user001' });

// 创建订单
const order = await mcp.call('create_order', {
  userId: 'user001',
  serviceType: 'REPAIR',
  description: '空调维修',
  amount: 200.0
});

// 发送通知
await mcp.call('send_notification', {
  userId: 'user001',
  type: 'SMS',
  title: '订单状态更新',
  content: '您的订单已完成支付'
});
```

### 通义 Lingma 接入

通义 Lingma 是阿里云推出的智能编程助手，支持通过 MCP 协议接入外部服务。

#### 1. 安装通义 Lingma

**VS Code 扩展**:
1. 打开 VS Code
2. 在扩展市场搜索 "通义 Lingma"
3. 点击安装 "Alibaba Cloud AI Coding Assistant"

**JetBrains IDE 插件**:
1. 打开 IntelliJ IDEA / PyCharm / WebStorm
2. 进入 `File > Settings > Plugins`
3. 搜索 "通义 Lingma" 并安装

#### 2. 配置 MCP 服务连接

**VS Code 配置**:

在 VS Code 设置中添加 MCP 服务配置：

```json
{
  "tongyi.lingma.mcp.servers": [
    {
      "name": "wanshifu-mcp",
      "displayName": "万师傅业务服务",
      "endpoint": "https://your-mcp-server.com/mcp",
      "apiKey": "your-api-key-here",
      "description": "万师傅内部业务系统集成",
      "enabled": true,
      "timeout": 30000,
      "retryCount": 3
    }
  ],
  "tongyi.lingma.mcp.autoConnect": true,
  "tongyi.lingma.mcp.logLevel": "info"
}
```

**JetBrains IDE 配置**:

1. 打开 `头像 > 个人设置 > MCP服务`
2. 在 "手动添加/配置文件" 添加MCP：

```
服务名称: 万师傅业务服务
服务地址: https://your-mcp-server.com/mcp
API 密钥: your-api-key-here
超时时间: 30000ms
启用状态: ✓ 已启用
```

#### 3. 创建 MCP 配置文件

在项目根目录创建 `.lingma-mcp.json` 配置文件：

```json
{
  "version": "1.0",
  "servers": {
    "wanshifu": {
      "name": "万师傅业务服务",
      "endpoint": "https://your-mcp-server.com/mcp",
      "authentication": {
        "type": "api-key",
        "key": "your-api-key-here"
      },
      "capabilities": {
        "tools": true,
        "resources": true,
        "prompts": true
      },
      "settings": {
        "timeout": 30000,
        "retryCount": 3,
        "enableCache": true,
        "logLevel": "info"
      }
    }
  },
  "defaultServer": "wanshifu",
  "globalSettings": {
    "enableAutoCompletion": true,
    "enableCodeSuggestion": true,
    "enableToolTips": true
  }
}
```

#### 4. 使用万师傅 MCP 工具

**在代码注释中使用**:

```javascript
// @lingma-mcp: 使用万师傅服务查询用户信息
// 用户ID: user001
const user = await queryUser('user001');

// @lingma-mcp: 创建维修订单
// 用户: user001, 服务类型: 空调维修, 金额: 200元
const order = await createOrder({
  userId: 'user001',
  serviceType: 'REPAIR',
  description: '空调维修',
  amount: 200.0
});
```

**通过 Lingma 聊天界面**:

```
用户: 帮我查询用户 user001 的信息

Lingma: 我来帮您查询用户信息。
[调用万师傅MCP工具: query_user]
参数: {"userId": "user001"}

查询结果：
- 用户ID: user001
- 用户名: 张三
- 邮箱: <EMAIL>
- 状态: 活跃
- 注册时间: 2024-01-15
```

**智能代码生成**:

```python
# 输入注释：创建一个函数来处理订单状态更新
# Lingma 会自动调用万师傅 MCP 工具生成代码

def update_order_status(order_id, new_status, reason=None):
    """
    更新订单状态
    通过万师傅MCP服务更新订单状态
    """
    # Lingma 自动生成的代码，调用 MCP 工具
    result = mcp_client.call_tool('update_order_status', {
        'orderId': order_id,
        'status': new_status,
        'reason': reason or '系统自动更新'
    })

    if result.get('success'):
        print(f"订单 {order_id} 状态已更新为 {new_status}")
        return True
    else:
        print(f"更新失败: {result.get('message')}")
        return False
```

#### 5. 高级功能配置

**自定义工具映射**:

在 `.lingma-mcp.json` 中配置工具映射：

```json
{
  "toolMappings": {
    "查询用户": {
      "mcpTool": "query_user",
      "description": "查询用户详细信息",
      "parameters": {
        "userId": {
          "type": "string",
          "description": "用户唯一标识符",
          "required": true
        }
      },
      "examples": [
        "查询用户 user001 的信息",
        "获取用户详情",
        "用户信息查询"
      ]
    },
    "创建订单": {
      "mcpTool": "create_order",
      "description": "创建新的服务订单",
      "parameters": {
        "userId": {"type": "string", "required": true},
        "serviceType": {"type": "string", "required": true},
        "description": {"type": "string", "required": true},
        "amount": {"type": "number", "required": true}
      },
      "examples": [
        "为用户创建维修订单",
        "新建服务订单",
        "订单创建"
      ]
    }
  }
}
```

**智能提示配置**:

```json
{
  "intelliSense": {
    "enableMcpTools": true,
    "showToolDescriptions": true,
    "enableParameterHints": true,
    "enableAutoImport": true,
    "customSnippets": [
      {
        "name": "万师傅用户查询",
        "prefix": "wsf-query-user",
        "body": [
          "// 查询万师傅用户信息",
          "const user = await mcpClient.callTool('query_user', {",
          "  userId: '${1:user001}'",
          "});",
          "console.log('用户信息:', user);"
        ],
        "description": "快速生成用户查询代码"
      }
    ]
  }
}
```

#### 6. 验证接入效果

**测试连接**:

1. 在 Lingma 聊天界面输入：`测试万师傅MCP连接`
2. 查看连接状态和可用工具列表
3. 尝试调用一个简单的工具进行验证

**功能验证**:

```
用户输入: 帮我查询所有待处理的订单

Lingma 响应:
✅ 已连接万师傅MCP服务
🔧 调用工具: query_order
📋 查询条件: status=PENDING

查询结果:
- 订单001: 空调维修 (用户: 张三)
- 订单002: 洗衣机维修 (用户: 李四)
- 订单003: 热水器安装 (用户: 王五)

共找到 3 个待处理订单。
```

#### 7. 常见问题解决

**连接失败**:
```json
{
  "troubleshooting": {
    "connectionFailed": {
      "symptoms": ["连接超时", "认证失败"],
      "solutions": [
        "检查网络连接",
        "验证API密钥是否正确",
        "确认服务器地址格式",
        "检查防火墙设置"
      ]
    },
    "toolNotFound": {
      "symptoms": ["工具未找到", "方法不存在"],
      "solutions": [
        "刷新工具列表",
        "检查工具名称拼写",
        "验证服务端工具注册",
        "查看服务端日志"
      ]
    }
  }
}
```

**性能优化**:
```json
{
  "performance": {
    "enableCache": true,
    "cacheTimeout": 300000,
    "batchRequests": true,
    "maxConcurrentRequests": 5,
    "requestTimeout": 30000
  }
}
```

### 自定义 AI 应用接入

如果您要开发自定义的 AI 应用，可以直接使用 HTTP API 接入。

#### 1. JavaScript/Node.js 示例

```javascript
class WanshifuMCPClient {
  constructor(serverUrl, apiKey) {
    this.serverUrl = serverUrl;
    this.apiKey = apiKey;
    this.requestId = 1;
  }

  async initialize() {
    const response = await fetch(`${this.serverUrl}/initialize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'initialize',
        params: {
          protocolVersion: '2025-06-18',
          capabilities: {
            roots: { listChanged: true },
            sampling: {},
            elicitation: {}
          },
          clientInfo: {
            name: 'Custom AI App',
            version: '1.0.0'
          }
        },
        id: this.requestId++
      })
    });

    return await response.json();
  }

  async listTools() {
    const response = await fetch(`${this.serverUrl}/tools/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/list',
        id: this.requestId++
      })
    });

    return await response.json();
  }

  async callTool(toolName, arguments) {
    const response = await fetch(`${this.serverUrl}/tools/call`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: arguments
        },
        id: this.requestId++
      })
    });

    return await response.json();
  }
}

// 使用示例
const client = new WanshifuMCPClient(
  'https://your-mcp-server.com/mcp',
  'your-api-key-here'
);

// 初始化连接
await client.initialize();

// 获取可用工具
const tools = await client.listTools();
console.log('Available tools:', tools.result.tools);

// 调用工具
const result = await client.callTool('query_user', { userId: 'user001' });
console.log('Tool result:', result.result);
```

#### 2. Python 示例

```python
import requests
import json

class WanshifuMCPClient:
    def __init__(self, server_url, api_key):
        self.server_url = server_url
        self.api_key = api_key
        self.request_id = 1
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        })

    def initialize(self):
        payload = {
            'jsonrpc': '2.0',
            'method': 'initialize',
            'params': {
                'protocolVersion': '2025-06-18',
                'capabilities': {
                    'roots': {'listChanged': True},
                    'sampling': {},
                    'elicitation': {}
                },
                'clientInfo': {
                    'name': 'Python AI App',
                    'version': '1.0.0'
                }
            },
            'id': self.request_id
        }
        self.request_id += 1

        response = self.session.post(f'{self.server_url}/initialize', json=payload)
        return response.json()

    def list_tools(self):
        payload = {
            'jsonrpc': '2.0',
            'method': 'tools/list',
            'id': self.request_id
        }
        self.request_id += 1

        response = self.session.post(f'{self.server_url}/tools/list', json=payload)
        return response.json()

    def call_tool(self, tool_name, arguments):
        payload = {
            'jsonrpc': '2.0',
            'method': 'tools/call',
            'params': {
                'name': tool_name,
                'arguments': arguments
            },
            'id': self.request_id
        }
        self.request_id += 1

        response = self.session.post(f'{self.server_url}/tools/call', json=payload)
        return response.json()

# 使用示例
client = WanshifuMCPClient(
    'https://your-mcp-server.com/mcp',
    'your-api-key-here'
)

# 初始化连接
init_result = client.initialize()
print('Initialization:', init_result)

# 获取可用工具
tools = client.list_tools()
print('Available tools:', tools['result']['tools'])

# 调用工具
result = client.call_tool('query_user', {'userId': 'user001'})
print('Tool result:', result['result'])
```

### 接入验证

完成配置后，可以通过以下方式验证接入是否成功：

#### 1. 基础连接测试

```bash
# 测试服务连通性
curl -X POST https://your-mcp-server.com/mcp/initialize \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key-here" \
  -d '{
    "jsonrpc": "2.0",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-06-18",
      "clientInfo": {"name": "Test Client", "version": "1.0.0"}
    },
    "id": "1"
  }'
```

#### 2. 工具列表测试

```bash
# 获取可用工具列表
curl -X POST https://your-mcp-server.com/mcp/tools/list \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key-here" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/list",
    "id": "2"
  }'
```

#### 3. 工具调用测试

```bash
# 测试工具调用
curl -X POST https://your-mcp-server.com/mcp/tools/call \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key-here" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "query_user",
      "arguments": {"userId": "user001"}
    },
    "id": "3"
  }'
```

#### 4. AI 客户端验证

**Claude Desktop 验证**:
- 在 Claude 对话中输入：`请帮我查询用户 user001 的信息`
- 观察是否能正确调用万师傅 MCP 工具

**Cursor IDE 验证**:
- 在代码编辑器中输入注释：`// 查询用户信息`
- 查看是否有 MCP 工具的智能提示

**通义 Lingma 验证**:
- 在 Lingma 聊天界面输入：`连接万师傅MCP服务状态如何？`
- 尝试输入：`帮我查询用户 user001 的订单信息`
- 在代码中输入：`// @lingma-mcp: 查询用户信息` 查看自动补全

### 常见问题解决

#### 1. 连接失败

**问题**: 无法连接到 MCP 服务
**解决方案**:
- 检查服务器地址和端口是否正确
- 验证 API Key 是否有效
- 确认网络连接和防火墙设置

#### 2. 认证错误

**问题**: 401 Unauthorized 错误
**解决方案**:
- 检查 API Key 格式是否正确
- 确认 API Key 是否在服务端配置中
- 验证请求头格式是否正确

#### 3. 工具调用失败

**问题**: 工具调用返回错误
**解决方案**:
- 检查工具名称是否正确
- 验证参数格式是否符合 Schema 要求
- 查看服务端日志获取详细错误信息

#### 4. 通义 Lingma 特定问题

**问题**: Lingma 无法识别 MCP 工具
**解决方案**:
- 确认 `.lingma-mcp.json` 配置文件格式正确
- 重启 IDE 并重新加载配置
- 检查 Lingma 插件版本是否支持 MCP

**问题**: 智能提示不工作
**解决方案**:
- 启用 `intelliSense.enableMcpTools` 配置
- 清除 Lingma 缓存并重新连接
- 验证工具映射配置是否正确

**问题**: 代码生成不准确
**解决方案**:
- 优化工具描述和参数说明
- 添加更多使用示例到配置中
- 调整 `toolMappings` 中的示例和描述

> 💡 **提示**: 更多接入问题和解决方案，请参考 [故障排查指南](docs/TROUBLESHOOTING.md)

## 配置管理

### 本地配置

服务支持多环境配置，主要配置文件为 `src/main/resources/application.yml`：

```yaml
# MCP 服务配置
mcp:
  protocol-version: "2025-06-18"
  security:
    api-keys:
      - demo-api-key-123456
      - wanshifu-api-key-789012
  tools:
    enabled: true
    auto-scan: true
    rate-limit:
      enabled: true
      requests-per-minute: 100

# Spring Boot 配置
server:
  port: 8080
spring:
  profiles:
    active: dev
```

### Apollo 配置中心（推荐）

对于生产环境，推荐使用 Apollo 配置中心进行动态配置管理：

```bash
# 设置环境变量
export APOLLO_META_SERVER=http://your-apollo-server:8080
export APOLLO_ENV=PROD
export APOLLO_APP_ID=mcp-service-demo
```

Apollo 配置示例：
```properties
# 安全配置
mcp.security.api-keys[0]=prod-api-key-secure-123
mcp.security.api-keys[1]=backup-api-key-456

# 性能配置
mcp.tools.rate-limit.requests-per-minute=500
mcp.tools.rate-limit.enabled=true

# 日志配置
logging.level.com.wanshifu.mcp=INFO
```

> 📖 详细配置说明请参考：[配置指南](docs/CONFIGURATION.md)

## API 使用指南

### 认证方式

服务支持两种认证方式：

**方式一：X-API-Key 请求头**
```bash
curl -H "X-API-Key: your-api-key" [其他参数...]
```

**方式二：Authorization Bearer**
```bash
curl -H "Authorization: Bearer your-api-key" [其他参数...]
```

### 核心 API

#### 1. 获取可用工具列表

```bash
curl -X POST http://localhost:8080/mcp/tools/list \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/list",
    "id": "1"
  }'
```

<details>
<summary>响应示例</summary>

```json
{
  "jsonrpc": "2.0",
  "result": {
    "tools": [
      {
        "name": "query_user",
        "description": "查询用户信息",
        "inputSchema": {
          "type": "object",
          "properties": {
            "userId": {"type": "string", "description": "用户ID"}
          },
          "required": ["userId"]
        }
      }
    ]
  },
  "id": "1"
}
```
</details>

#### 2. 用户管理

**查询用户信息**
```bash
curl -X POST http://localhost:8080/mcp/tools/call \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "query_user",
      "arguments": {"userId": "user001"}
    },
    "id": "2"
  }'
```

#### 3. 订单管理

**查询订单**
```bash
curl -X POST http://localhost:8080/mcp/tools/call \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "query_order",
      "arguments": {
        "userId": "user001",
        "status": "PENDING"
      }
    },
    "id": "3"
  }'
```

**更新订单状态**
```bash
curl -X POST http://localhost:8080/mcp/tools/call \
  -H "Content-Type: application/json" \
  -H "X-API-Key: demo-api-key-123456" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "update_order_status",
      "arguments": {
        "orderId": "order002",
        "status": "PAID",
        "reason": "用户已完成支付"
      }
    },
    "id": "4"
  }'
```

> 📖 完整 API 文档请参考：[API 参考](docs/API.md)

## 开发指南

### 添加新的 MCP 工具

1. **创建工具方法**：在业务服务类中添加带有 `@McpTool` 注解的方法

```java
@McpTool(
    name = "create_user",
    description = "创建新用户",
    inputSchema = """
        {
          "type": "object",
          "properties": {
            "username": {"type": "string", "description": "用户名"},
            "email": {"type": "string", "description": "邮箱地址"}
          },
          "required": ["username", "email"]
        }
        """
)
public Map<String, Object> createUser(Map<String, Object> arguments) {
    // 实现业务逻辑
    return Map.of("success", true, "userId", "new-user-id");
}
```

2. **工具自动注册**：服务启动时会自动扫描并注册所有 `@McpTool` 注解的方法

3. **测试工具**：使用提供的测试脚本验证新工具

```bash
./scripts/test-upgraded-service.sh
```

### 集成真实业务系统

1. **数据库集成**：添加 JPA 或 MyBatis 依赖，配置数据源
2. **微服务调用**：使用 RestTemplate 或 WebClient 调用其他服务
3. **消息队列**：集成 RabbitMQ 或 Kafka 进行异步处理
4. **缓存支持**：添加 Redis 缓存提升性能

### 安全最佳实践

| 安全措施 | 实现方式 | 优先级 |
|----------|----------|--------|
| HTTPS 加密 | 配置 SSL 证书 | 🔴 必须 |
| API Key 管理 | 定期轮换，强密钥策略 | 🔴 必须 |
| 请求限流 | 实现 Rate Limiting | 🟡 推荐 |
| IP 白名单 | 配置允许访问的 IP 范围 | 🟡 推荐 |
| 审计日志 | 记录所有 API 调用 | 🟢 可选 |

## 监控运维

### 健康检查

```bash
# 应用健康状态
curl http://localhost:8080/actuator/health

# 详细健康信息
curl http://localhost:8080/actuator/health/details

# MCP 服务状态
curl -H "X-API-Key: demo-api-key-123456" \
     http://localhost:8080/mcp/health
```

### 性能监控

```bash
# JVM 指标
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# HTTP 请求指标
curl http://localhost:8080/actuator/metrics/http.server.requests

# 自定义业务指标
curl http://localhost:8080/actuator/metrics/mcp.tools.calls
```

### 日志管理

- **日志位置**：`logs/mcp-service.log`
- **日志级别**：可通过配置动态调整
- **日志格式**：结构化 JSON 格式，便于日志分析

```bash
# 实时查看日志
tail -f logs/mcp-service.log

# 查看错误日志
grep "ERROR" logs/mcp-service.log
```

## 技术栈

| 组件 | 版本 | 说明 |
|------|------|------|
| **运行时** | | |
| Java | 21 LTS | 现代 Java 特性支持 |
| Spring Boot | 3.3.5 | 企业级应用框架 |
| Spring Security | 6.x | 安全认证框架 |
| **配置管理** | | |
| Apollo | 2.3.0 | 分布式配置中心 |
| **序列化** | | |
| Jackson | 2.17.2 | JSON 处理 |
| **安全** | | |
| JWT | 0.12.6 | Token 认证 |
| **构建工具** | | |
| Maven | 3.9+ | 项目构建管理 |
| **容器化** | | |
| Docker | 20.10+ | 容器化部署 |

## 支持的 AI 客户端

| AI 客户端 | 版本要求 | 接入方式 | 特色功能 |
|-----------|----------|----------|----------|
| **Claude Desktop** | v1.0+ | 配置文件 + 桥接脚本 | 原生 MCP 协议支持 |
| **Cursor IDE** | v0.40+ | 扩展插件 | 智能代码补全和生成 |
| **通义 Lingma** | 最新版 | 配置文件 + 工具映射 | 中文智能提示，代码生成 |
| **自定义应用** | - | HTTP API | 完全自定义集成方案 |

## 文档导航

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [🏗️ 架构设计](docs/ARCHITECTURE.md) | 系统架构和设计原理 | 架构师、开发者 |
| [⚙️ 配置指南](docs/CONFIGURATION.md) | 详细配置说明和最佳实践 | 运维、开发者 |
| [📖 API 参考](docs/API.md) | 完整的 API 接口文档 | 集成开发者 |
| [🔒 安全指南](docs/SECURITY.md) | 安全配置和防护措施 | 安全工程师 |
| [🚀 部署指南](docs/DEPLOYMENT.md) | 生产环境部署最佳实践 | 运维工程师 |
| [🔧 开发指南](docs/DEVELOPMENT.md) | 开发环境搭建和扩展开发 | 开发者 |

## 社区支持

- **问题反馈**：[GitHub Issues](https://github.com/wanshifu/mcp-service-demo/issues)
- **功能建议**：[GitHub Discussions](https://github.com/wanshifu/mcp-service-demo/discussions)
- **技术交流**：加入万师傅技术交流群

## 版本历史

| 版本 | 发布日期 | 主要更新 |
|------|----------|----------|
| v1.0.0 | 2024-07-22 | 初始版本，支持 MCP 2025-06-18 |
| v1.1.0 | 计划中 | 增强安全特性，性能优化 |

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**万师傅技术团队** © 2024 | 让 AI 更好地服务企业业务
